/**
 * 🔒 SISTEMA SICUREZZA CREDENZIALI TURN ULTRA-AVANZATO
 * Genera token temporanei con scadenza e rotazione automatica
 */

const crypto = require('crypto');

class TurnCredentialsManager {
    constructor() {
        // 🔐 CONFIGURAZIONE SICURA (da variabili ambiente)
        this.turnSecret = process.env.TURN_SECRET || this.generateSecureSecret();
        this.turnServers = [
            {
                url: process.env.TURN_SERVER_1 || 'turn:videochatcouple.com:3478',
                protocols: ['udp', 'tcp']
            },
            {
                url: process.env.TURN_SERVER_2 || 'turns:videochatcouple.com:5349',
                protocols: ['tcp']
            }
        ];
        
        // ⏰ CONFIGURAZIONE TEMPORALE
        this.tokenDuration = 12 * 60 * 60; // 12 ore in secondi
        this.cleanupInterval = 60 * 60 * 1000; // 1 ora in ms
        
        // 📊 CACHE TOKEN ATTIVI
        this.activeTokens = new Map();
        
        console.log('🔒 Turn Credentials Manager inizializzato');
        this.startCleanupTimer();
    }
    
    // 🔐 Genera segreto sicuro se non fornito
    generateSecureSecret() {
        const secret = crypto.randomBytes(32).toString('hex');
        console.warn('⚠️ TURN_SECRET non configurato, generato automaticamente');
        console.warn('💡 Aggiungi TURN_SECRET al file .env per persistenza');
        return secret;
    }
    
    // 🎯 Genera credenziali temporanee per un utente
    generateTemporaryCredentials(userId, userIP) {
        const timestamp = Math.floor(Date.now() / 1000);
        const expiry = timestamp + this.tokenDuration;
        
        // 🔑 Username temporaneo con scadenza
        const tempUsername = `${expiry}:${userId}`;
        
        // 🔐 Password HMAC-SHA1 sicura
        const hmac = crypto.createHmac('sha1', this.turnSecret);
        hmac.update(tempUsername);
        const tempPassword = hmac.digest('base64');
        
        // 📝 Genera configurazione ICE completa
        const iceServers = this.generateIceServers(tempUsername, tempPassword);
        
        // 💾 Salva token per monitoraggio
        const tokenId = crypto.randomUUID();
        this.activeTokens.set(tokenId, {
            userId,
            userIP,
            username: tempUsername,
            expiry: expiry * 1000, // Converti a ms
            createdAt: Date.now()
        });
        
        console.log(`🔑 Token generato per utente ${userId} (scade: ${new Date(expiry * 1000).toISOString()})`);
        
        return {
            tokenId,
            iceServers,
            expiresAt: expiry * 1000,
            validFor: this.tokenDuration
        };
    }
    
    // 🏗️ Genera configurazione ICE servers completa
    generateIceServers(username, password) {
        const servers = [
            // Server STUN pubblici (sempre disponibili)
            { urls: "stun:stun.l.google.com:19302" },
            { urls: "stun:stun1.l.google.com:19302" },
            { urls: "stun:stun2.l.google.com:19302" },
            { urls: "stun:stun3.l.google.com:19302" },
            { urls: "stun:stun4.l.google.com:19302" }
        ];
        
        // Aggiungi server TURN con credenziali temporanee
        this.turnServers.forEach(server => {
            server.protocols.forEach(protocol => {
                const turnUrl = protocol === 'tcp' && server.url.startsWith('turns:') 
                    ? server.url 
                    : `${server.url.replace('turns:', 'turn:')}?transport=${protocol}`;
                
                servers.push({
                    urls: turnUrl,
                    username: username,
                    credential: password
                });
            });
        });
        
        return servers;
    }
    
    // 🔍 Valida token esistente
    validateToken(tokenId) {
        const token = this.activeTokens.get(tokenId);
        if (!token) {
            return { valid: false, reason: 'Token non trovato' };
        }
        
        if (Date.now() > token.expiry) {
            this.activeTokens.delete(tokenId);
            return { valid: false, reason: 'Token scaduto' };
        }
        
        return { valid: true, token };
    }
    
    // 🔄 Rinnova token esistente
    renewToken(tokenId, userId, userIP) {
        const validation = this.validateToken(tokenId);
        if (!validation.valid) {
            // Token non valido, genera nuovo
            return this.generateTemporaryCredentials(userId, userIP);
        }
        
        // Token ancora valido, controlla se vicino alla scadenza
        const timeLeft = validation.token.expiry - Date.now();
        const renewThreshold = 2 * 60 * 60 * 1000; // 2 ore
        
        if (timeLeft < renewThreshold) {
            console.log(`🔄 Rinnovo token per utente ${userId} (scade tra ${Math.round(timeLeft/60000)} minuti)`);
            this.activeTokens.delete(tokenId);
            return this.generateTemporaryCredentials(userId, userIP);
        }
        
        return {
            tokenId,
            iceServers: this.generateIceServers(validation.token.username, 
                this.generatePasswordForUsername(validation.token.username)),
            expiresAt: validation.token.expiry,
            validFor: Math.floor(timeLeft / 1000)
        };
    }
    
    // 🔐 Rigenera password per username esistente
    generatePasswordForUsername(username) {
        const hmac = crypto.createHmac('sha1', this.turnSecret);
        hmac.update(username);
        return hmac.digest('base64');
    }
    
    // 🧹 Cleanup automatico token scaduti
    startCleanupTimer() {
        setInterval(() => {
            const now = Date.now();
            let cleaned = 0;
            
            for (const [tokenId, token] of this.activeTokens.entries()) {
                if (now > token.expiry) {
                    this.activeTokens.delete(tokenId);
                    cleaned++;
                }
            }
            
            if (cleaned > 0) {
                console.log(`🧹 Cleanup: rimossi ${cleaned} token scaduti`);
            }
            
            console.log(`📊 Token attivi: ${this.activeTokens.size}`);
        }, this.cleanupInterval);
    }
    
    // 📊 Statistiche sistema
    getStats() {
        const now = Date.now();
        const tokens = Array.from(this.activeTokens.values());
        
        return {
            activeTokens: tokens.length,
            expiringSoon: tokens.filter(t => t.expiry - now < 60 * 60 * 1000).length, // < 1 ora
            oldestToken: tokens.length > 0 ? Math.min(...tokens.map(t => t.createdAt)) : null,
            newestToken: tokens.length > 0 ? Math.max(...tokens.map(t => t.createdAt)) : null,
            turnServers: this.turnServers.length,
            tokenDuration: this.tokenDuration
        };
    }
    
    // 🔒 Revoca token specifico
    revokeToken(tokenId) {
        const deleted = this.activeTokens.delete(tokenId);
        if (deleted) {
            console.log(`🔒 Token ${tokenId} revocato`);
        }
        return deleted;
    }
    
    // 🚨 Revoca tutti i token di un utente
    revokeUserTokens(userId) {
        let revoked = 0;
        for (const [tokenId, token] of this.activeTokens.entries()) {
            if (token.userId === userId) {
                this.activeTokens.delete(tokenId);
                revoked++;
            }
        }
        
        if (revoked > 0) {
            console.log(`🚨 Revocati ${revoked} token per utente ${userId}`);
        }
        
        return revoked;
    }
}

// Esporta singleton
const turnCredentialsManager = new TurnCredentialsManager();

module.exports = {
    TurnCredentialsManager,
    turnCredentialsManager
};
