/*
 * VideoChat Elite - Professional WebRTC Server (Matching Completamente Casuale)
 */

require('dotenv').config();

const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');
const mongoose = require('mongoose');
const compression = require('compression');
const { createAdapter } = require("@socket.io/redis-adapter");
const { createClient } = require("redis");
const rateLimit = require('express-rate-limit');
const sanitizeHtml = require('sanitize-html');

// Systems
const { cacheBuster } = require('./cache_buster');
const { turnCredentialsManager } = require('./turn_credentials');

const app = express();
const server = http.createServer(app);

// CORS for production
const allowedOrigins = [
  'https://videochatcouple.com',
  'https://www.videochatcouple.com'
];

const io = socketIo(server, {
  cors: { origin: allowedOrigins, methods: ['GET','POST'], credentials: true },
  pingInterval: 20000,
  pingTimeout: 10000,
  perMessageDeflate: { threshold: 1024 }
});

const PORT = process.env.PORT || 8080;

app.use(compression({ level: 6 }));

const limiter = rateLimit({
  windowMs: 15 * 60 * 1000,
  max: 100,
  standardHeaders: true,
  legacyHeaders: false,
  message: 'Troppe richieste da questo IP, riprova più tardi.'
});
app.use(limiter);

app.use((req, res, next) => {
  res.setHeader('Strict-Transport-Security', 'max-age=63072000; includeSubDomains; preload');
  res.setHeader('Content-Security-Policy', "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.socket.io https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdnjs.cloudflare.com; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; connect-src 'self' wss:; img-src 'self' data:");
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  if (req.url.match(/\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$/)) {
    res.setHeader('Cache-Control', 'public, max-age=31536000, immutable');
  } else {
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');
  }
  next();
});

app.set('trust proxy', 1);
app.use(express.json({ limit: '10mb' }));

// Cache buster middleware
app.use(cacheBuster.getExpressMiddleware());

// Static files
app.use(express.static(path.join(__dirname)));

// Mongo models (single declaration)
const banSchema = new mongoose.Schema({
  userId: { type: String, required: true, index: true },
  ipAddress: { type: String, required: true, index: true },
  reason: { type: String, required: true },
  timestamp: { type: Date, default: Date.now, index: true },
  reportedBy: { type: String, default: 'admin' }
});
const reportSchema = new mongoose.Schema({
  reportedUserId: { type: String, required: true, index: true },
  reporterIp: { type: String, required: true, index: true },
  reasonCategory: { type: String, required: true },
  detailedDescription: { type: String, required: true },
  userAgent: { type: String },
  timestamp: { type: Date, default: Date.now, index: true }
});
const Ban = mongoose.model('Ban', banSchema);
const Report = mongoose.model('Report', reportSchema);

// Connect Mongo
mongoose.connect(process.env.MONGO_URI)
  .then(() => console.log('✅ MongoDB connesso.'))
  .catch(err => console.error('❌ Errore MongoDB:', err));


// --- Nuovo endpoint HTTP per invio segnalazioni DSA ---
app.post('/api/dsa/report', async (req, res) => {
  try {
    const { reportedUserId, reasonCategory, detailedDescription } = req.body || {};
    if (!reportedUserId || !reasonCategory || !detailedDescription) {
      return res.status(400).json({ success: false, error: 'Campi richiesti mancanti' });
    }
    const cleanDescription = sanitizeHtml(detailedDescription, { allowedTags: [], allowedAttributes: {} });
    const reporterIp = req.ip || req.connection.remoteAddress;
    const userAgent = req.headers['user-agent'] || '';
    const report = new Report({ reportedUserId, reporterIp, reasonCategory, detailedDescription: cleanDescription, userAgent });
    await report.save();
    return res.json({ success: true, message: 'Segnalazione inviata con successo.' });
  } catch (error) {
    console.error('Errore POST /api/dsa/report:', error);
    return res.status(500).json({ success: false, error: 'Errore interno del server' });
  }
});

// Cache control endpoints
app.get('/api/cache/status', (req, res) => res.json(cacheBuster.getStatus()));
app.post('/api/cache/refresh', (req, res) => res.json(cacheBuster.forceRefresh()));
app.get('/api/cache/clear', (req, res) => {
  const result = cacheBuster.forceRefresh();
  res.json({ ...result, message: 'Cache cleared successfully! Refresh your browser.', instructions: 'La cache è stata cancellata. Ricarica la pagina (F5 o Ctrl+R)' });
});

// TURN secure endpoints
app.post('/api/turn/credentials', (req, res) => {
  try {
    const userIP = req.ip || req.connection.remoteAddress;
    const userId = (req.body && req.body.userId) || `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const credentials = turnCredentialsManager.generateTemporaryCredentials(userId, userIP);
    res.json({ success: true, ...credentials });
  } catch (error) {
    console.error('❌ Errore generazione credenziali TURN:', error);
    res.status(500).json({ success: false, error: 'Errore interno del server' });
  }
});

app.post('/api/turn/renew', (req, res) => {
  try {
    const tokenId = req.body && req.body.tokenId;
    const userId = req.body && req.body.userId;
    const userIP = req.ip || req.connection.remoteAddress;
    if (!tokenId || !userId) return res.status(400).json({ success: false, error: 'TokenId e userId richiesti' });
    const credentials = turnCredentialsManager.renewToken(tokenId, userId, userIP);
    res.json({ success: true, ...credentials });
  } catch (error) {
    console.error('❌ Errore rinnovo credenziali TURN:', error);
    res.status(500).json({ success: false, error: 'Errore interno del server' });
  }
});

app.get('/api/turn/stats', (req, res) => {
  try {
    const stats = turnCredentialsManager.getStats();
    res.json({ success: true, stats, timestamp: new Date().toISOString() });
  } catch (error) {
    console.error('❌ Errore stats TURN:', error);
    res.status(500).json({ success: false, error: 'Errore interno del server' });
  }
});

// Dashboard redirect
app.get('/dashboard', (req, res) => res.redirect('/dashboard.html'));

// Redis adapter
const pubClient = createClient({ url: process.env.REDIS_URI });
const subClient = pubClient.duplicate();
Promise.all([pubClient.connect(), subClient.connect()])
  .then(() => { io.adapter(createAdapter(pubClient, subClient)); console.log('✅ Adattatore Redis connesso.'); })
  .catch((err) => { console.error('❌ Errore Redis:', err); });

// Matching state
const activeUsers = new Map();
const waitingUsers = new Map();
let waitingQueue = [];
const priorityWaiting = new Set(); // utenti con rematch prioritario ("Prossimo")

function removeFromQueue(socketId) {
  const userInQueue = waitingUsers.get(socketId);
  if (!userInQueue || userInQueue.queueIndex === undefined) return;
  const indexToRemove = userInQueue.queueIndex;
  const lastSocketId = waitingQueue.pop();
  if (indexToRemove < waitingQueue.length) {
    waitingQueue[indexToRemove] = lastSocketId;
    const movedUser = waitingUsers.get(lastSocketId);
    if (movedUser) movedUser.queueIndex = indexToRemove;
  }
  delete userInQueue.queueIndex;
}

function findRandomMatch() {
  // Priorità: servi prima chi ha premuto "Prossimo"
  for (const id of priorityWaiting) {
    const entry = waitingUsers.get(id);
    if (entry && io.sockets.sockets.get(id)) {
      priorityWaiting.delete(id);
      return entry;
    } else {
      priorityWaiting.delete(id);
    }
  }
  if (waitingQueue.length === 0) return null;
  const randomIndex = Math.floor(Math.random() * waitingQueue.length);
  const randomSocketId = waitingQueue[randomIndex];
  return waitingUsers.get(randomSocketId);
}

function instantRematch(socket, userInfo = {}) {
  try {
    let partnerEntry = null;
    while (waitingQueue.length > 0 && !partnerEntry) {
      const candidateId = waitingQueue.pop();
      const candidate = waitingUsers.get(candidateId);
      if (candidate && candidate.socketId !== socket.id && io.sockets.sockets.get(candidate.socketId)) {
        partnerEntry = candidate;
      }
    }
    if (partnerEntry) {
      waitingUsers.delete(partnerEntry.socketId);
      const partnerSocket = io.sockets.sockets.get(partnerEntry.socketId);
      if (!partnerSocket) return;
      const roomId = `${socket.id}_${partnerEntry.socketId}`;
      socket.join(roomId);
      partnerSocket.join(roomId);
      activeUsers.set(socket.id, { partnerId: partnerEntry.socketId, roomId });
      activeUsers.set(partnerEntry.socketId, { partnerId: socket.id, roomId });
      socket.emit('match-found', { partnerId: partnerEntry.socketId, isInitiator: true });
      partnerSocket.emit('match-found', { partnerId: socket.id, isInitiator: false });
      return;
    }
    // Nessuno disponibile: aggiungi a priorità
    if (!waitingUsers.has(socket.id)) {
      waitingUsers.set(socket.id, { socketId: socket.id, userInfo });
    }
    priorityWaiting.add(socket.id);
  } catch (e) { console.error('Errore instantRematch:', e); }
}

io.on('connection', (socket) => {
  const userIp = socket.handshake.address;

  socket.on('find-match', async (userInfo) => {
    try {
      const match = findRandomMatch();
      if (match && match.socketId !== socket.id) {
        const partnerSocket = io.sockets.sockets.get(match.socketId);
        if (!partnerSocket) {
          const newIndex = waitingQueue.push(socket.id) - 1;
          waitingUsers.set(socket.id, { socketId: socket.id, userInfo, queueIndex: newIndex });
          return;
        }
        removeFromQueue(match.socketId);
        waitingUsers.delete(match.socketId);
        const roomId = `${socket.id}_${match.socketId}`;
        socket.join(roomId);
        partnerSocket.join(roomId);
        activeUsers.set(socket.id, { partnerId: match.socketId, roomId });
        activeUsers.set(match.socketId, { partnerId: socket.id, roomId });
        socket.emit('match-found', { partnerId: match.socketId, isInitiator: true });
        partnerSocket.emit('match-found', { partnerId: socket.id, isInitiator: false });
      } else {
        if (!waitingUsers.has(socket.id)) {
          const newIndex = waitingQueue.push(socket.id) - 1;
          waitingUsers.set(socket.id, { socketId: socket.id, userInfo, queueIndex: newIndex });
        }
      }
    } catch (error) {
      console.error('Errore matching:', error);
      socket.emit('error', 'Errore durante la ricerca');

    }
  });

  // Chat relay
  socket.on('chat-message', (data = {}) => {
    if (!data.to || typeof data.message !== 'string') return;
    io.to(data.to).emit('chat-message', { from: socket.id, message: data.message });
  });

  socket.on('offer', (data) => io.to(data.to).emit('offer', { offer: data.offer, from: socket.id }));
  socket.on('answer', (data) => io.to(data.to).emit('answer', { answer: data.answer, from: socket.id }));
  socket.on('ice-candidate', (data) => io.to(data.to).volatile.emit('ice-candidate', { candidate: data.candidate, from: socket.id }));


  // --- Explicit content signaling ---
  socket.on('explicit-content-request', (data = {}) => {
    if (!data.to) return;
    io.to(data.to).emit('explicit-content-request', { from: socket.id });
  });
  socket.on('explicit-content-accept', (data = {}) => {
    if (!data.to) return;
    io.to(data.to).emit('explicit-content-accept', { from: socket.id });
  });
  socket.on('explicit-content-reject', (data = {}) => {
    if (!data.to) return;
    io.to(data.to).emit('explicit-content-reject', { from: socket.id });
    const targetId = data.to;
    // Forza next per entrambe le parti
    endCallAndCleanup(socket.id);
    const targetSocket = io.sockets.sockets.get(targetId);
    if (targetSocket) {
      endCallAndCleanup(targetId);
      instantRematch(targetSocket, { quickRematch: true });
    }
    instantRematch(socket, { quickRematch: true });
  });

  function endCallAndCleanup(socketId) {
    const user = activeUsers.get(socketId);
    if (user) {
      const partnerId = user.partnerId;
      const roomId = user.roomId;
      const partnerSocket = io.sockets.sockets.get(partnerId);
      if (partnerSocket) { partnerSocket.emit('call-ended-by-partner'); if (roomId) partnerSocket.leave(roomId); }
      const selfSocket = io.sockets.sockets.get(socketId);
      if (selfSocket && roomId) selfSocket.leave(roomId);
      activeUsers.delete(socketId);
      if (partnerId) activeUsers.delete(partnerId);
    }
  }

  socket.on('end-call', (data, ack) => { endCallAndCleanup(socket.id); if (typeof ack === 'function') ack({ ok: true }); });
  socket.on('next-partner', (data, ack) => { endCallAndCleanup(socket.id); instantRematch(socket, { quickRematch: true }); if (typeof ack === 'function') ack({ ok: true }); });
  socket.on('disconnect', () => {
    endCallAndCleanup(socket.id);
    if (waitingUsers.has(socket.id)) { removeFromQueue(socket.id); waitingUsers.delete(socket.id); }
    if (priorityWaiting.has(socket.id)) priorityWaiting.delete(socket.id);
  });
});

setInterval(() => {
  const totalUsers = io.sockets.sockets.size;
  io.emit('update-user-count', { count: totalUsers });
}, 15000);

server.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 VideoChat Elite Server avviato su porta ${PORT}`);
  console.log(`📄 Dashboard: http://localhost:${PORT}/dashboard.html`);
  console.log(`🎛️ Cache Control: http://localhost:${PORT}/cache_control.html`);
  console.log('🔧 Sistema Cache Busting: ATTIVO');
});

