<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VideoChat Elite Dashboard - Secure HD Video Chat</title>
    <meta name="description" content="VideoChat Elite Dashboard: enter the secure, professionally moderated HD video chat.">
    <link rel="canonical" href="https://videochatcouple.com/dashboard.html">
    <link rel="icon" type="image/x-icon" href="favicon.ico">
    
    <link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdnjs.cloudflare.com" crossorigin>

    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Exo+2:wght@300;400;600;700&display=swap" media="print" onload="this.media='all'">
    <noscript><link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Exo+2:wght@300;400;600;700&display=swap"></noscript>
    
    <style>
        body{font-family:'Exo 2',sans-serif;background:#000;color:#fff;margin:0;}
    </style>
    <!-- Critical CSS: keep header and controls visible ASAP -->
    <style>
        .main-container{max-width:1100px;margin:24px auto;padding:16px}
        .header{display:flex;align-items:center;justify-content:space-between;gap:12px}
        .header h1{font-size:20px;margin:0}
        .controls{display:flex;gap:12px;flex-wrap:wrap;margin-top:12px;align-items:center}
        .control-btn{border:none;color:#fff;cursor:pointer;display:flex;align-items:center;justify-content:center;transition:transform .08s ease-in-out, filter .15s}
        .control-btn:active{transform:scale(0.96)}
        .control-btn:disabled{opacity:.6;cursor:not-allowed;filter:none}
        /* Forza icone bianche e vivide nei pulsanti colorati */
        .control-btn .btn-icon path,
        .control-btn .btn-icon rect,
        .control-btn .btn-icon circle,
        .control-btn .btn-icon polygon,
        .control-btn .btn-icon line { fill:#fff; stroke:#fff; }
        .control-btn.btn-green,.control-btn.btn-red,.control-btn.btn-blue,.control-btn.btn-explicit{ color:#fff; }
        .btn-round{width:60px;height:60px;border-radius:50%;position:relative;box-shadow:0 4px 12px rgba(0,0,0,.35)}
        .btn-green{background-color:#10b981 !important}
        .btn-red{background-color:#ef4444 !important}
        .btn-blue{background-color:#3b82f6 !important}
        .btn-gray{background-color:#111 !important;border:1px solid #222 !important}
        .btn-explicit{background-color:#000 !important;border:1px solid #222 !important}
        .control-btn{-webkit-appearance:none;appearance:none}
        /* Explicit button: red flame icon */
        .control-btn.btn-explicit .btn-icon path,
        .control-btn.btn-explicit .btn-icon rect,
        .control-btn.btn-explicit .btn-icon circle,
        .control-btn.btn-explicit .btn-icon polygon,
        .control-btn.btn-explicit .btn-icon line { fill:#ff3b30 !important; stroke:#ff3b30 !important; }
        .btn-icon{width:26px;height:26px}
        .badge-18{position:absolute;bottom:-6px;right:-6px;background:#111;border:1px solid #222;color:#fff;border-radius:12px;font-size:10px;padding:2px 6px;line-height:1;box-shadow:0 2px 6px rgba(0,0,0,.35)}
        .video-container{background:#050505;border-radius:6px;padding:12px;margin-top:12px}
        /* Pulsazione elegante per fiamma espliciti */
        @keyframes pulseFire { 0%{ transform: scale(1); filter: drop-shadow(0 0 0 rgba(255,59,48,.0)); } 50%{ transform: scale(1.06); filter: drop-shadow(0 0 8px rgba(255,59,48,.45)); } 100%{ transform: scale(1); filter: drop-shadow(0 0 0 rgba(255,59,48,.0)); } }
        .control-btn.pulse { animation: pulseFire 1.2s ease-in-out infinite; }
        .control-btn.btn-explicit .btn-icon { will-change: transform, filter; }
        .control-btn.btn-explicit.explicit-on { border-color:#ff3b30 !important; box-shadow:0 0 12px rgba(255,59,48,.35); }
        /* Notifiche */
        .notif{position:relative;opacity:0;transform:translateY(-6px);transition:all .2s ease;background:#111;color:#fff;border:1px solid #222;padding:8px 12px;border-radius:6px;margin:6px 0}
        .notif.in{opacity:1;transform:none}
        .notif.success{border-color:#10b981}
        .notif.error{border-color:#ef4444}
        .notif.info{border-color:#3b82f6}
    </style>

    <link rel="stylesheet" href="elite-background.min.css" media="print" onload="this.media='all'">
    <noscript><link rel="stylesheet" href="elite-background.min.css"></noscript>
    <link rel="stylesheet" href="dashboard-styles.css" media="print" onload="this.media='all'">
    <noscript><link rel="stylesheet" href="dashboard-styles.css"></noscript>
    <!-- Replaced Font Awesome with inline SVG sprite for smaller payload -->
    <!-- SVG icons are embedded inline in the body to avoid loading the full icon font -->
    
</head>
<body>
    <div class="main-container">
        <div class="header">
            <h1>VideoChat Elite</h1>
            <p>Secure & Private Connection</p>
            <div class="live-users">
                <svg width="18" height="18" viewBox="0 0 24 24" aria-hidden="true" focusable="false"><title>Users</title><circle cx="8" cy="8" r="3" fill="currentColor"/><circle cx="17" cy="7" r="2" fill="currentColor"/><path d="M2 20c0-3 4-5 8-5s8 2 8 5v1H2v-1z" fill="currentColor"/></svg>
                Live Users: <span id="userCount">0</span>
            </div>
        </div>
        
        <div class="video-container" style="min-height: 424px;"><h2>Your Video</h2><div class="video-wrapper"><video id="localVideo" autoplay muted playsinline style="display:none;"></video><div id="localPlaceholder" class="video-placeholder"><h3>Camera Off</h3><p>Enable camera to start</p></div></div></div>
        <div class="video-container" style="min-height: 424px;"><h2>Partner's Video</h2><div class="video-wrapper"><video id="remoteVideo" autoplay playsinline style="display:none;"></video><div id="remotePlaceholder" class="video-placeholder"><h3>Waiting...</h3><p>Start searching</p></div></div></div>
        
        <div class="controls">
            <button id="audioBtn" class="control-btn btn-round btn-gray" onclick="toggleAudio()" title="Microfono">
                <svg class="btn-icon" viewBox="0 0 24 24" aria-hidden="true"><title>Mic</title><rect x="10" y="3" width="4" height="10" rx="2" fill="currentColor"/><path d="M5 11v1a7 7 0 0 0 14 0v-1" stroke="currentColor" fill="none"/></svg>
            </button>
            <button id="cameraBtn" class="control-btn btn-round btn-gray" onclick="toggleCamera()" title="Fotocamera">
                <svg class="btn-icon" viewBox="0 0 24 24" aria-hidden="true"><title>Camera</title><rect x="3" y="6" width="14" height="11" rx="2" ry="2" stroke="currentColor" fill="none"/><circle cx="12" cy="11.5" r="3" fill="currentColor"/></svg>
            </button>
            <button id="matchBtn" class="control-btn btn-round btn-green" onclick="findMatch()" title="Chiama / Cerca">
                <svg class="btn-icon" viewBox="0 0 24 24" aria-hidden="true"><title>Chiama</title>
                    <path d="M6.62 10.79c3.06 4.87 7.72 9.53 12.59 ********** 1.09.23 1.49-.17l1.8-1.8c.3-.3.4-.76.24-1.15-.31-.84-.53-1.45-.59-1.9a1 1 0 00-1.02-.84h-2.35c-.27 0-.53.11-.72.3l-1.03 1.03a11.05 11.05 0 01-6.01-6.01l1.03-1.03c.19-.19.3-.45.3-.72V6.67c0-.47-.35-.87-.84-1.02-.45-.06-1.06-.28-1.9-.59-.39-.16-.85-.06-1.15.24l-1.8 1.8c-.4.4-.47 1.01-.17 1.49z" fill="#fff"/>
                </svg>
            </button>
            <button id="skipBtn" class="control-btn btn-round btn-blue" onclick="skipUser()" disabled title="Prossimo">
                <svg class="btn-icon" viewBox="0 0 24 24" aria-hidden="true"><title>Next</title><path d="M5 4l9 8-9 8V4z" fill="#fff"/><rect x="17" y="4" width="2" height="16" fill="#fff"/></svg>
            </button>
            <button id="endCallBtn" class="control-btn btn-round btn-red" onclick="endCall()" disabled title="Chiudi Chiamata">
                <svg class="btn-icon" viewBox="0 0 24 24" aria-hidden="true"><title>Fine chiamata</title>
                    <path d="M3 14c5-3 13-3 18 0v2.5a1 1 0 01-1 1h-4.5a1 1 0 01-1-1v-.8c-1.9-.4-4.1-.4-6 0v.8a1 1 0 01-1 1H4a1 1 0 01-1-1V14z" fill="#fff"/>
                </svg>
            </button>
            <button id="explicitBtn" class="control-btn btn-round btn-explicit" onclick="requestExplicitContent()" disabled title="Contenuti espliciti">
                <svg class="btn-icon" viewBox="0 0 24 24" aria-hidden="true"><title>Explicit</title>
                    <!-- Red flame icon -->
                    <path d="M12 2c2 3 3 4 3 6 0 2-1.2 3.5-3 4-1.5-.5-3-2-3-4 0-1.5.7-2.6 1.6-3.7C9.1 5 8 6.5 8 9c0 3.9 3 7 4 9 1-2 4-5.1 4-9 0-3-1.4-5-4-7z"/>
                </svg>
            </button>
            <button id="reportBtn" class="control-btn btn-round btn-gray" onclick="showDsaReportModal()" disabled title="Segnala (DSA)">
                <svg class="btn-icon" viewBox="0 0 24 24" aria-hidden="true"><title>Segnala</title>
                    <path d="M12 2l7 4v6c0 4.1-2.9 7.9-7 9-4.1-1.1-7-4.9-7-9V6l7-4z" fill="none" stroke="#fff"/>
                    <rect x="11" y="8" width="2" height="6" fill="#fff"/>
                    <circle cx="12" cy="17" r="1.2" fill="#fff"/>
                </svg>
            </button>
            <button id="iosBtn" class="control-btn btn-round btn-gray" onclick="enableIOSVideo()" style="display:none;" title="Attiva Video iOS">
                <svg class="btn-icon" viewBox="0 0 24 24" aria-hidden="true"><title>iOS</title><path d="M16 2c-.3 1.2-.8 2.2-1.6 3.1C13.9 6.2 12.6 7 11 7s-2.9-.8-3.4-1.9C6.2 4.2 5.7 3.2 5.4 2H16z" fill="#fff"/></svg>
            </button>
        </div>

        <div class="chat-container">
            <h2>Chat</h2><div id="chatMessages" class="chat-messages"></div>
            <div class="chat-input-container"><input type="text" id="chatInput" class="chat-input" placeholder="Type a message..." disabled><button id="chatSendBtn" class="control-btn" title="Send Message" disabled>
                <svg width="18" height="18" viewBox="0 0 24 24" aria-hidden="true"><title>Send</title><path d="M2 21l21-9L2 3v7l15 2-15 2v7z" fill="currentColor"/></svg>
            </button></div>
        </div>
    </div>

    <footer>
        &copy; 2025 VideoChat Elite. All rights reserved.
    </footer>
    
    <div id="explicitModal" class="report-modal" style="display:none;"><div class="report-modal-overlay"></div><div class="report-modal-content"><div class="report-modal-header"><h3>Explicit Content Request</h3></div><div class="report-modal-body"><p>Your partner requests to enable explicit content. Do you accept?</p></div><div class="report-actions"><button onclick="rejectExplicitContent()" class="report-btn-cancel">Decline</button><button onclick="acceptExplicitContent()" class="report-btn-submit">Accept</button></div></div></div>
    <div id="dsaReportModal" style="display:none;"><div class="report-modal-overlay"></div><div class="report-modal-content"><div class="report-modal-header"><h3>Report Illegal Content</h3><button class="report-close-btn">&times;</button><div class="report-dsa-badge">DSA Art. 16</div></div><div class="report-modal-body"><div class="report-field"><label for="report-category">Category of illegal content:</label><select id="report-category"><option value="">-- Select a reason --</option><option value="hate_speech">Hate Speech</option><option value="harassment">Harassment</option><option value="child_sexual_abuse">Child Sexual Abuse</option><option value="other_illegal">Other Illegal Content</option></select></div><div class="report-field"><label for="report-description">Provide a description:</label><textarea id="report-description" placeholder="Explain why you believe this content is illegal..."></textarea></div></div><div class="report-actions"><button class="report-btn-cancel">Cancel</button><button id="submitDsaReportBtn" class="report-btn-submit">Submit Report</button></div></div></div>
    <div id="notification-container" style="position: fixed; top: 20px; right: 20px; z-index: 10001;"></div>
    
    <script src="dashboard.js" defer></script>

    <script defer src="elite-background.min.js"></script>
</body>
</html>