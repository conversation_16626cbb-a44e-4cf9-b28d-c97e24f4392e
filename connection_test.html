<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Connessione WebRTC</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: #fff;
            padding: 20px;
            margin: 0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border: 1px solid #444;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .status.success { background: #2d5a2d; border: 1px solid #4a8a4a; }
        .status.error { background: #5a2d2d; border: 1px solid #8a4a4a; }
        .status.info { background: #2d4a5a; border: 1px solid #4a7a8a; }
        button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #005a99; }
        button:disabled { background: #555; cursor: not-allowed; }
        #results {
            background: #000;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Test Connessione WebRTC per Dispositivi Mobili</h1>
        
        <div class="test-section">
            <h2>📱 Informazioni Dispositivo</h2>
            <div id="deviceInfo"></div>
        </div>
        
        <div class="test-section">
            <h2>🌐 Test Connessione</h2>
            <button onclick="testConnection()">Avvia Test Connessione</button>
            <button onclick="testSTUN()">Test Server STUN</button>
            <button onclick="testTURN()">Test Server TURN</button>
            <button onclick="clearResults()">Pulisci Risultati</button>
            <div id="connectionStatus"></div>
        </div>
        
        <div class="test-section">
            <h2>📊 Risultati Test</h2>
            <div id="results"></div>
        </div>
    </div>

    <script src="mobile_webrtc_config.js"></script>
    <script>
        let testResults = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}`;
            testResults.push(logMessage);
            
            const resultsDiv = document.getElementById('results');
            resultsDiv.textContent = testResults.join('\n');
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
            
            console.log(logMessage);
        }
        
        function clearResults() {
            testResults = [];
            document.getElementById('results').textContent = '';
        }
        
        function showDeviceInfo() {
            const info = document.getElementById('deviceInfo');
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            
            let connectionInfo = { type: 'unknown' };
            if (typeof window.MobileWebRTCConfig !== 'undefined') {
                connectionInfo = window.MobileWebRTCConfig.getConnectionType();
            }
            
            info.innerHTML = `
                <div class="status info">
                    <strong>Dispositivo:</strong> ${isMobile ? 'Mobile' : 'Desktop'}<br>
                    <strong>User Agent:</strong> ${navigator.userAgent}<br>
                    <strong>Tipo Connessione:</strong> ${connectionInfo.type}<br>
                    <strong>Downlink:</strong> ${connectionInfo.downlink || 'N/A'} Mbps<br>
                    <strong>RTT:</strong> ${connectionInfo.rtt || 'N/A'} ms<br>
                    <strong>Save Data:</strong> ${connectionInfo.saveData ? 'Attivo' : 'Disattivo'}
                </div>
            `;
        }
        
        async function testConnection() {
            log('🚀 Avvio test connessione completo...');
            
            try {
                // Test configurazione ICE
                let config;
                if (typeof window.MobileWebRTCConfig !== 'undefined') {
                    config = window.MobileWebRTCConfig.getDynamicIceConfig();
                    log('✅ Configurazione ICE dinamica caricata');
                } else {
                    log('⚠️ Configurazione ICE dinamica non disponibile, uso fallback');
                    config = {
                        iceServers: [
                            { urls: "stun:stun.l.google.com:19302" }
                        ]
                    };
                }
                
                log(`🔧 Configurazione ICE: ${config.iceTransportPolicy || 'all'}`);
                log(`🔧 Server ICE configurati: ${config.iceServers.length}`);
                
                // Crea peer connection di test
                const pc = new RTCPeerConnection(config);
                
                pc.onicecandidate = (event) => {
                    if (event.candidate) {
                        const c = event.candidate;
                        log(`🧊 ICE Candidate: ${c.type} ${c.protocol} ${c.address}:${c.port}`);
                    } else {
                        log('✅ ICE gathering completato');
                    }
                };
                
                pc.oniceconnectionstatechange = () => {
                    log(`🔗 ICE Connection State: ${pc.iceConnectionState}`);
                };
                
                // Crea offer per iniziare ICE gathering
                const offer = await pc.createOffer({
                    offerToReceiveAudio: true,
                    offerToReceiveVideo: true
                });
                
                await pc.setLocalDescription(offer);
                log('✅ Offer creato e ICE gathering avviato');
                
                // Attendi un po' per raccogliere candidates
                setTimeout(() => {
                    pc.close();
                    log('🔒 Test connessione completato');
                }, 10000);
                
            } catch (error) {
                log(`❌ Errore test connessione: ${error.message}`, 'error');
            }
        }
        
        async function testSTUN() {
            log('🧊 Test server STUN...');
            
            const stunServers = [
                "stun:stun.l.google.com:19302",
                "stun:stun1.l.google.com:19302",
                "stun:videochatcouple.com:3478"
            ];
            
            for (const stunUrl of stunServers) {
                try {
                    const pc = new RTCPeerConnection({
                        iceServers: [{ urls: stunUrl }]
                    });
                    
                    let candidateFound = false;
                    
                    pc.onicecandidate = (event) => {
                        if (event.candidate && event.candidate.type === 'srflx') {
                            candidateFound = true;
                            log(`✅ STUN ${stunUrl}: OK - ${event.candidate.address}:${event.candidate.port}`);
                        }
                    };
                    
                    const offer = await pc.createOffer();
                    await pc.setLocalDescription(offer);
                    
                    setTimeout(() => {
                        if (!candidateFound) {
                            log(`❌ STUN ${stunUrl}: Nessun candidate ricevuto`);
                        }
                        pc.close();
                    }, 5000);
                    
                } catch (error) {
                    log(`❌ STUN ${stunUrl}: Errore - ${error.message}`);
                }
            }
        }
        
        async function testTURN() {
            log('🔄 Test server TURN...');
            
            const turnServers = [
                {
                    urls: "turn:videochatcouple.com:3478?transport=udp",
                    username: "Casudda93",
                    credential: "Leonida1993!"
                },
                {
                    urls: "turn:videochatcouple.com:3478?transport=tcp",
                    username: "Casudda93",
                    credential: "Leonida1993!"
                }
            ];
            
            for (const turnServer of turnServers) {
                try {
                    const pc = new RTCPeerConnection({
                        iceServers: [turnServer],
                        iceTransportPolicy: 'relay' // Solo TURN
                    });
                    
                    let candidateFound = false;
                    
                    pc.onicecandidate = (event) => {
                        if (event.candidate && event.candidate.type === 'relay') {
                            candidateFound = true;
                            log(`✅ TURN ${turnServer.urls}: OK - ${event.candidate.address}:${event.candidate.port}`);
                        }
                    };
                    
                    const offer = await pc.createOffer();
                    await pc.setLocalDescription(offer);
                    
                    setTimeout(() => {
                        if (!candidateFound) {
                            log(`❌ TURN ${turnServer.urls}: Nessun candidate relay ricevuto`);
                        }
                        pc.close();
                    }, 8000);
                    
                } catch (error) {
                    log(`❌ TURN ${turnServer.urls}: Errore - ${error.message}`);
                }
            }
        }
        
        // Inizializza la pagina
        document.addEventListener('DOMContentLoaded', () => {
            showDeviceInfo();
            log('📱 Test connessione WebRTC inizializzato');
        });
    </script>
</body>
</html>
