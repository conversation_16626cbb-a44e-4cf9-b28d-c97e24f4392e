<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Cache Control - VideoChat Elite</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: #fff;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        h1 {
            text-align: center;
            color: #00d4ff;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
        }
        .control-section {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .control-section h2 {
            color: #00ff88;
            margin-top: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        button {
            background: linear-gradient(45deg, #00d4ff, #00ff88);
            color: #000;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            font-size: 16px;
            margin: 10px 5px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 212, 255, 0.5);
        }
        button:active {
            transform: translateY(0);
        }
        button.danger {
            background: linear-gradient(45deg, #ff4757, #ff6b7a);
            color: white;
        }
        .status-display {
            background: #000;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #333;
        }
        .success { color: #00ff88; }
        .error { color: #ff4757; }
        .info { color: #00d4ff; }
        .warning { color: #ffa502; }
        .timestamp {
            color: #888;
            font-size: 12px;
        }
        .quick-actions {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            justify-content: center;
            margin: 20px 0;
        }
        .file-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }
        .file-item {
            background: rgba(255, 255, 255, 0.05);
            padding: 10px;
            border-radius: 5px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .file-name {
            font-weight: bold;
            color: #00d4ff;
        }
        .file-hash {
            font-family: monospace;
            font-size: 12px;
            color: #888;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Cache Control Panel</h1>
        
        <div class="control-section">
            <h2>⚡ Azioni Rapide</h2>
            <div class="quick-actions">
                <button onclick="clearCache()">🗑️ Cancella Cache</button>
                <button onclick="refreshCache()">🔄 Aggiorna Cache</button>
                <button onclick="checkStatus()">📊 Stato Cache</button>
                <button onclick="forceReload()" class="danger">💥 Ricarica Forzata</button>
            </div>
        </div>
        
        <div class="control-section">
            <h2>📊 Stato Sistema</h2>
            <div id="statusDisplay" class="status-display">
                Clicca "Stato Cache" per vedere le informazioni...
            </div>
        </div>
        
        <div class="control-section">
            <h2>📁 File Monitorati</h2>
            <div id="fileList" class="file-list">
                Caricamento...
            </div>
        </div>
        
        <div class="control-section">
            <h2>🔧 Istruzioni</h2>
            <p><strong>Cancella Cache:</strong> Forza l'aggiornamento di tutti i file JS/CSS</p>
            <p><strong>Aggiorna Cache:</strong> Rigenera gli hash dei file</p>
            <p><strong>Stato Cache:</strong> Mostra informazioni dettagliate</p>
            <p><strong>Ricarica Forzata:</strong> Cancella cache + ricarica pagina</p>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const display = document.getElementById('statusDisplay');
            const timestamp = new Date().toLocaleTimeString();
            const logLine = `[${timestamp}] ${message}\n`;
            
            const span = document.createElement('span');
            span.className = type;
            span.textContent = logLine;
            
            display.appendChild(span);
            display.scrollTop = display.scrollHeight;
        }
        
        async function clearCache() {
            log('🗑️ Cancellazione cache in corso...', 'info');
            
            try {
                const response = await fetch('/api/cache/clear');
                const result = await response.json();
                
                if (result.success) {
                    log('✅ Cache cancellata con successo!', 'success');
                    log(`📅 Nuova versione: ${result.version}`, 'info');
                    log('💡 Ricarica la pagina principale per vedere le modifiche', 'warning');
                } else {
                    log('❌ Errore durante cancellazione cache', 'error');
                }
            } catch (error) {
                log(`❌ Errore: ${error.message}`, 'error');
            }
        }
        
        async function refreshCache() {
            log('🔄 Aggiornamento cache in corso...', 'info');
            
            try {
                const response = await fetch('/api/cache/refresh', { method: 'POST' });
                const result = await response.json();
                
                if (result.success) {
                    log('✅ Cache aggiornata!', 'success');
                    log(`📅 Versione: ${result.version}`, 'info');
                    checkStatus(); // Aggiorna stato
                } else {
                    log('❌ Errore durante aggiornamento', 'error');
                }
            } catch (error) {
                log(`❌ Errore: ${error.message}`, 'error');
            }
        }
        
        async function checkStatus() {
            log('📊 Controllo stato cache...', 'info');
            
            try {
                const response = await fetch('/api/cache/status');
                const status = await response.json();
                
                log('📊 STATO CACHE:', 'success');
                log(`📅 Versione: ${status.version}`, 'info');
                log(`📁 File monitorati: ${status.filesWatched}`, 'info');
                log(`🕒 Ultimo aggiornamento: ${new Date(status.lastUpdate).toLocaleString()}`, 'info');
                
                // Aggiorna lista file
                updateFileList(status.fileHashes);
                
            } catch (error) {
                log(`❌ Errore: ${error.message}`, 'error');
            }
        }
        
        function updateFileList(fileHashes) {
            const fileList = document.getElementById('fileList');
            fileList.innerHTML = '';
            
            Object.entries(fileHashes).forEach(([filename, hash]) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.innerHTML = `
                    <div class="file-name">${filename}</div>
                    <div class="file-hash">Hash: ${hash}</div>
                `;
                fileList.appendChild(fileItem);
            });
        }
        
        function forceReload() {
            log('💥 Ricarica forzata in corso...', 'warning');
            
            clearCache().then(() => {
                setTimeout(() => {
                    log('🔄 Ricaricando pagina...', 'info');
                    window.location.reload(true);
                }, 2000);
            });
        }
        
        // Carica stato iniziale
        document.addEventListener('DOMContentLoaded', () => {
            log('🚀 Cache Control Panel caricato', 'success');
            checkStatus();
        });
    </script>
</body>
</html>
