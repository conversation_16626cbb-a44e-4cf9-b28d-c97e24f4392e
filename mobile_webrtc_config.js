/**
 * Configurazione WebRTC Avanzata per Dispositivi Mobili
 * Ottimizzata per connessioni dati mobili e NAT simmetrici
 */

// 🧠 SISTEMA DI RILEVAMENTO RETE ULTRA-INTELLIGENTE
function getAdvancedConnectionInfo() {
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
    const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
    const isIOSSafari = isIOS && isSafari;

    let connectionInfo = { type: 'unknown', downlink: 0, rtt: 0, saveData: false };

    // Rilevamento connessione nativa
    if ('connection' in navigator) {
        const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
        if (connection) {
            connectionInfo = {
                type: connection.effectiveType || connection.type,
                downlink: connection.downlink || 0,
                rtt: connection.rtt || 0,
                saveData: connection.saveData || false
            };
        }
    }

    // 🎯 CLASSIFICAZIONE INTELLIGENTE DELLA CONNESSIONE
    const isSlowConnection = ['slow-2g', '2g'].includes(connectionInfo.type);
    const isMediumConnection = ['3g'].includes(connectionInfo.type);
    const isFastConnection = ['4g', '5g'].includes(connectionInfo.type);
    const isVerySlowConnection = connectionInfo.rtt > 2000 || connectionInfo.downlink < 0.5;
    const isProbablyMobileData = isMobile && (isSlowConnection || isMediumConnection || connectionInfo.saveData);
    const isProbablyNAT = isMobile && !connectionInfo.type.includes('wifi');

    // 🔍 CALCOLO PUNTEGGIO DIFFICOLTÀ CONNESSIONE (0-100)
    let difficultyScore = 0;
    if (isMobile) difficultyScore += 20;
    if (isSlowConnection) difficultyScore += 40;
    if (isMediumConnection) difficultyScore += 25;
    if (isVerySlowConnection) difficultyScore += 30;
    if (isProbablyMobileData) difficultyScore += 25;
    if (connectionInfo.saveData) difficultyScore += 15;
    if (connectionInfo.rtt > 1000) difficultyScore += 20;
    if (connectionInfo.downlink < 1) difficultyScore += 15;

    return {
        ...connectionInfo,
        isMobile,
        isIOS,
        isSafari,
        isIOSSafari,
        isSlowConnection,
        isMediumConnection,
        isFastConnection,
        isVerySlowConnection,
        isProbablyMobileData,
        isProbablyNAT,
        difficultyScore: Math.min(difficultyScore, 100),
        connectionQuality: difficultyScore < 30 ? 'excellent' :
                          difficultyScore < 50 ? 'good' :
                          difficultyScore < 70 ? 'poor' : 'terrible'
    };
}

// Mantieni compatibilità
function getConnectionType() {
    const info = getAdvancedConnectionInfo();
    return {
        type: info.type,
        downlink: info.downlink,
        rtt: info.rtt,
        saveData: info.saveData
    };
}

// 🎯 SISTEMA ICE MULTI-STRATEGIA ULTRA-INTELLIGENTE
function getSmartIceConfig(strategy = 'adaptive') {
    const connInfo = getAdvancedConnectionInfo();

    console.log(`🧠 Analisi Connessione: ${connInfo.connectionQuality} (${connInfo.difficultyScore}/100)`);
    console.log(`📱 Mobile: ${connInfo.isMobile}, iOS: ${connInfo.isIOS}, Dati: ${connInfo.isProbablyMobileData}, NAT: ${connInfo.isProbablyNAT}`);

    // 🏗️ CONFIGURAZIONE BASE POTENZIATA
    const baseServers = [
        // STUN servers multipli per ridondanza
        { urls: "stun:stun.l.google.com:19302" },
        { urls: "stun:stun1.l.google.com:19302" },
        { urls: "stun:stun2.l.google.com:19302" },
        { urls: "stun:stun3.l.google.com:19302" },
        { urls: "stun:stun4.l.google.com:19302" }
        // 🔒 I server TURN vengono forniti dinamicamente dal backend sicuro
    ];

    // 🎯 STRATEGIE MULTIPLE BASATE SU DIFFICOLTÀ
    const strategies = {
        // Strategia 1: Ottimistica (connessioni buone)
        'optimistic': {
            iceServers: baseServers,
            iceTransportPolicy: 'all',
            iceCandidatePoolSize: 20,
            iceGatheringTimeout: 8000,
            bundlePolicy: 'max-bundle',
            rtcpMuxPolicy: 'require'
        },

        // Strategia 2: Bilanciata (connessioni medie)
        'balanced': {
            iceServers: baseServers,
            iceTransportPolicy: 'all',
            iceCandidatePoolSize: 12,
            iceGatheringTimeout: 15000,
            bundlePolicy: 'max-bundle',
            rtcpMuxPolicy: 'require'
        },

        // Strategia 3: Aggressiva (connessioni difficili)
        'aggressive': {
            iceServers: baseServers,
            iceTransportPolicy: 'relay', // Solo TURN
            iceCandidatePoolSize: 8,
            iceGatheringTimeout: 10000,
            bundlePolicy: 'max-bundle',
            rtcpMuxPolicy: 'require'
        },

        // 🍎 Strategia 4: iOS Specifica
        'ios': {
            iceServers: baseServers,
            iceTransportPolicy: 'all', // iOS funziona meglio con 'all'
            iceCandidatePoolSize: 8,
            iceGatheringTimeout: 25000,
            bundlePolicy: 'max-bundle',
            rtcpMuxPolicy: 'require',
            // Configurazioni specifiche iOS
            sdpSemantics: 'unified-plan'
        },

        // Strategia 4: Adattiva (scelta automatica)
        'adaptive': null // Calcolata sotto
    };

    // 🧠 SELEZIONE STRATEGIA INTELLIGENTE
    if (strategy === 'adaptive') {
        // 🍎 PRIORITÀ iOS
        if (connInfo.isIOS) {
            strategy = 'ios';
            console.log('🍎 Strategia: iOS SPECIFICA (dispositivo Apple)');
        } else if (connInfo.difficultyScore < 30) {
            strategy = 'optimistic';
            console.log('🚀 Strategia: OTTIMISTICA (connessione eccellente)');
        } else if (connInfo.difficultyScore < 50) {
            strategy = 'optimistic';
            console.log('🚀 Strategia: OTTIMISTICA (connessione buona)');
        } else if (connInfo.difficultyScore < 70) {
            strategy = 'balanced';
            console.log('⚖️ Strategia: BILANCIATA (connessione media)');
        } else {
            strategy = 'aggressive';
            console.log('🔥 Strategia: AGGRESSIVA (connessione difficile)');
        }
    }

    return strategies[strategy];
}

// Mantieni compatibilità
function getDynamicIceConfig() {
    return getSmartIceConfig('adaptive');
}

// 📱 CONFIGURAZIONE MEDIA OTTIMIZZATA PER iOS/SAFARI
function getOptimizedMediaConstraints() {
    const connInfo = getAdvancedConnectionInfo();
    const isSlowConnection = ['slow-2g', '2g', '3g'].includes(connInfo.type);

    // 🍎 OTTIMIZZAZIONI SPECIFICHE PER iOS
    if (connInfo.isIOS) {
        return {
            video: {
                width: { ideal: 640, max: 1280 },
                height: { ideal: 480, max: 720 },
                frameRate: { ideal: 24, max: 30 }, // iOS preferisce 24fps
                facingMode: 'user',
                // Ottimizzazioni iOS specifiche
                aspectRatio: { ideal: 4/3 },
                resizeMode: 'crop-and-scale'
            },
            audio: {
                echoCancellation: true,
                noiseSuppression: true,
                autoGainControl: true,
                sampleRate: 44100, // iOS preferisce 44.1kHz
                channelCount: 1 // Mono per ridurre bandwidth
            }
        };
    }

    // 📱 CONFIGURAZIONE MOBILE GENERICA
    if (connInfo.isMobile || isSlowConnection) {
        return {
            video: {
                width: { ideal: 480, max: 640 },
                height: { ideal: 360, max: 480 },
                frameRate: { ideal: 15, max: 24 },
                facingMode: 'user'
            },
            audio: {
                echoCancellation: true,
                noiseSuppression: true,
                autoGainControl: true,
                sampleRate: 16000
            }
        };
    }

    // 💻 CONFIGURAZIONE DESKTOP
    return {
        video: {
            width: { ideal: 1280, max: 1920 },
            height: { ideal: 720, max: 1080 },
            frameRate: { ideal: 30 }
        },
        audio: {
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true
        }
    };
}

// Monitoraggio qualità connessione WebRTC
function monitorConnectionQuality(peerConnection) {
    if (!peerConnection) return;
    
    const monitor = setInterval(async () => {
        try {
            const stats = await peerConnection.getStats();
            let inboundRtp = null;
            let outboundRtp = null;
            
            stats.forEach(report => {
                if (report.type === 'inbound-rtp' && report.mediaType === 'video') {
                    inboundRtp = report;
                }
                if (report.type === 'outbound-rtp' && report.mediaType === 'video') {
                    outboundRtp = report;
                }
            });
            
            if (inboundRtp) {
                console.log(`📊 Qualità Video IN: ${inboundRtp.framesPerSecond || 0} fps, ` +
                           `${Math.round((inboundRtp.bytesReceived || 0) / 1024)} KB ricevuti`);
            }
            
            if (outboundRtp) {
                console.log(`📊 Qualità Video OUT: ${outboundRtp.framesPerSecond || 0} fps, ` +
                           `${Math.round((outboundRtp.bytesSent || 0) / 1024)} KB inviati`);
            }
            
        } catch (error) {
            console.warn('Errore monitoraggio stats:', error);
        }
    }, 5000);
    
    // Cleanup quando la connessione si chiude
    peerConnection.addEventListener('connectionstatechange', () => {
        if (peerConnection.connectionState === 'closed' || 
            peerConnection.connectionState === 'failed') {
            clearInterval(monitor);
        }
    });
    
    return monitor;
}

// Diagnostica connessione per debug
async function diagnoseConnection(peerConnection) {
    if (!peerConnection) return;
    
    try {
        const stats = await peerConnection.getStats();
        const candidates = [];
        const connections = [];
        
        stats.forEach(report => {
            if (report.type === 'local-candidate') {
                candidates.push(`LOCAL: ${report.candidateType} ${report.protocol} ${report.address}:${report.port}`);
            }
            if (report.type === 'remote-candidate') {
                candidates.push(`REMOTE: ${report.candidateType} ${report.protocol} ${report.address}:${report.port}`);
            }
            if (report.type === 'candidate-pair' && report.state === 'succeeded') {
                connections.push(`ATTIVA: ${report.localCandidateId} -> ${report.remoteCandidateId}`);
            }
        });
        
        console.log('🔍 DIAGNOSI CONNESSIONE:');
        console.log('ICE Candidates:', candidates);
        console.log('Connessioni Attive:', connections);
        
        return { candidates, connections };
        
    } catch (error) {
        console.error('Errore diagnosi:', error);
        return null;
    }
}

// 🚀 SISTEMA DI ESCALATION AUTOMATICA ULTRA-AVANZATO
class ConnectionEscalationManager {
    constructor() {
        this.currentStrategy = 'adaptive';
        this.attemptCount = 0;
        this.maxAttempts = 4;
        this.escalationTimer = null;
        this.connectionStartTime = null;
        this.strategies = ['optimistic', 'balanced', 'aggressive', 'aggressive'];
    }

    // 🎯 Avvia processo di connessione con escalation
    startConnection(peerConnection, onEscalate) {
        this.attemptCount = 0;
        this.connectionStartTime = Date.now();
        this.currentStrategy = 'adaptive';
        this.onEscalate = onEscalate;

        console.log('🚀 AVVIO SISTEMA ESCALATION INTELLIGENTE');
        this.monitorConnection(peerConnection);

        return getSmartIceConfig(this.currentStrategy);
    }

    // 🔍 Monitora connessione e attiva escalation se necessario
    monitorConnection(peerConnection) {
        if (!peerConnection) return;

        // Timer di escalation intelligente
        const escalationDelay = this.getEscalationDelay();
        console.log(`⏱️ Timer escalation: ${escalationDelay/1000}s`);

        this.escalationTimer = setTimeout(() => {
            this.handleEscalation(peerConnection);
        }, escalationDelay);

        // Monitor stati connessione
        const originalStateHandler = peerConnection.onconnectionstatechange;
        peerConnection.onconnectionstatechange = () => {
            const state = peerConnection.connectionState;
            console.log(`🔗 Connection State: ${state} (Tentativo ${this.attemptCount + 1})`);

            if (state === 'connected' || state === 'completed') {
                this.onSuccess();
            } else if (state === 'failed') {
                this.handleEscalation(peerConnection);
            }

            if (originalStateHandler) originalStateHandler();
        };

        // Monitor ICE connection
        const originalIceHandler = peerConnection.oniceconnectionstatechange;
        peerConnection.oniceconnectionstatechange = () => {
            const iceState = peerConnection.iceConnectionState;
            console.log(`🧊 ICE State: ${iceState}`);

            if (iceState === 'connected' || iceState === 'completed') {
                this.onSuccess();
            } else if (iceState === 'failed') {
                this.handleEscalation(peerConnection);
            }

            if (originalIceHandler) originalIceHandler();
        };
    }

    // ⚡ Gestisce escalation automatica
    handleEscalation(peerConnection) {
        if (this.escalationTimer) {
            clearTimeout(this.escalationTimer);
            this.escalationTimer = null;
        }

        this.attemptCount++;

        if (this.attemptCount >= this.maxAttempts) {
            console.log('❌ ESCALATION FALLITA - Massimo tentativi raggiunto');
            return false;
        }

        const nextStrategy = this.strategies[this.attemptCount - 1];
        console.log(`🔄 ESCALATION ${this.attemptCount}/${this.maxAttempts}: ${nextStrategy.toUpperCase()}`);

        // Notifica per ricreare connessione
        if (this.onEscalate) {
            this.onEscalate(nextStrategy, this.attemptCount);
        }

        return true;
    }

    // ✅ Connessione riuscita
    onSuccess() {
        if (this.escalationTimer) {
            clearTimeout(this.escalationTimer);
            this.escalationTimer = null;
        }

        const duration = Date.now() - this.connectionStartTime;
        console.log(`✅ CONNESSIONE RIUSCITA in ${duration}ms con strategia ${this.currentStrategy.toUpperCase()}`);
    }

    // ⏱️ Calcola delay escalation basato su strategia corrente
    getEscalationDelay() {
        const connInfo = getAdvancedConnectionInfo();
        const baseDelay = connInfo.difficultyScore < 30 ? 2000 :
                         connInfo.difficultyScore < 60 ? 5000 : 8000;

        return baseDelay + (this.attemptCount * 2000);
    }

    // 🛑 Ferma escalation
    stop() {
        if (this.escalationTimer) {
            clearTimeout(this.escalationTimer);
            this.escalationTimer = null;
        }
    }
}

// Esporta le funzioni per l'uso in dashboard.js
if (typeof window !== 'undefined') {
    window.MobileWebRTCConfig = {
        getDynamicIceConfig,
        getSmartIceConfig,
        getAdvancedConnectionInfo,
        getOptimizedMediaConstraints,
        monitorConnectionQuality,
        diagnoseConnection,
        getConnectionType,
        ConnectionEscalationManager
    };
}
