Deployment notes — VideoChat Elite (production)

This project includes several client-side and server-side improvements to optimize performance, security and reliability.

Key client changes applied:
- Lazy-load Socket.IO client on user action (Find Partner). Reduces initial JS payload and improves Time to Interactive.
- Inline critical CSS for header/controls to speed up FCP.
- Replaced Font Awesome with inline SVG icons to avoid loading the whole font library.
- Lazy-load `mobile_webrtc_config.js` when MobileWebRTCConfig is required.
- Added script loader with timeout and fallback to local vendor copy (`/vendor/socket.io.min.js`) before CDN.
- Added helpers to update SVG icons instead of manipulating Font Awesome class names.

Recommended production steps (apply on your server):
1) Vendorize Socket.IO client
   - Download a matching `socket.io.min.js` for your Socket.IO server version into `/vendor/socket.io.min.js`.

2) Build and bundle client
   - Install dev dependencies and build (optional):
     npm install --production=false
     npm run build:client
   - This produces `dist/dashboard.bundle.js`. You can serve that instead of raw `dashboard.js`.

3) Add Subresource Integrity (SRI)
   - When loading 3rd-party scripts from CDN, add integrity and crossorigin attributes.

4) Server / CDN / nginx recommendations
   - Enable Brotli or gzip compression.
   - Use aggressive cache headers for static assets with fingerprinting (Cache-Control: public, max-age=31536000, immutable).
   - Keep HTML responses with `no-cache` so you control when clients fetch new asset manifest.

5) Security
   - Keep a strict CSP header. Server already includes a CSP but update if you vendorize assets.
   - Ensure HTTPS/HSTS is enabled. Server.js already sets HSTS header.

6) Monitoring & observability
   - Add health endpoints and instrument WebRTC statistics reporting to server logs.
   - Use the existing TURN credentials endpoints; ensure TURN servers are highly available.

If you want, I can:
- Create `dist/dashboard.bundle.js` via esbuild and wire `dashboard.html` to use it.
- Generate a `/vendor/socket.io.min.js` file and add an integrity hash.
- Add a small nginx sample with recommended caching and compression settings.
Tell me which of the above to proceed with and I will implement it.

Quick production steps added:

1) Vendorize Socket.IO client (local):
   - Run: npm ci && npm run vendorize
   - This will copy `socket.io-client/dist/socket.io.min.js` into `/vendor/socket.io.min.js`.

2) Protect admin dashboard:
   - Set `ADMIN_USER` and `ADMIN_PASS` in your production `.env` to enable Basic Auth for `/admin` pages.

3) Retention:
   - The server has a TTL index that removes telemetry older than 30 days automatically.

4) Build (optional):
   - npm run build:client

5) Start server:
   - NODE_ENV=production PORT=8080 node server.js

If you want, I can vendorize the socket client for you now (create `/vendor/socket.io.min.js`) and add SRI hashes to the HTML. Choose: vendorize now / create E2E tests / wire bundle in `dashboard.html`.
