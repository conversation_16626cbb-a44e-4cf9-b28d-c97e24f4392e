const fs = require('fs');
const path = require('path');

(async function vendorize() {
  try {
    const clientPath = path.join(__dirname, '..', 'node_modules', 'socket.io-client', 'dist', 'socket.io.min.js');
    const outDir = path.join(__dirname, '..', 'vendor');
    const outPath = path.join(outDir, 'socket.io.min.js');

    if (!fs.existsSync(clientPath)) {
      console.error('socket.io-client not found in node_modules. Run npm install first.');
      process.exit(1);
    }

    if (!fs.existsSync(outDir)) fs.mkdirSync(outDir);
    fs.copyFileSync(clientPath, outPath);
    console.log('Vendored socket.io client to', outPath);
  } catch (e) {
    console.error('Vendorize failed', e);
    process.exit(1);
  }
})();
