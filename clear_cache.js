#!/usr/bin/env node

/**
 * 🚀 SCRIPT RAPIDO PER CANCELLARE CACHE
 * Uso: node clear_cache.js
 */

const { cacheBuster } = require('./cache_buster');

console.log('🚀 VIDEOCHAT ELITE - CACHE CLEANER');
console.log('=====================================');

// Forza refresh cache
const result = cacheBuster.forceRefresh();

if (result.success) {
    console.log('✅ CACHE CANCELLATA CON SUCCESSO!');
    console.log(`📅 Nuova versione: ${result.version}`);
    console.log('💡 Ricarica il browser per vedere le modifiche');
    
    // Mostra stato
    const status = cacheBuster.getStatus();
    console.log('\n📊 STATO CACHE:');
    console.log(`📁 File monitorati: ${status.filesWatched}`);
    console.log(`🕒 Ultimo aggiornamento: ${new Date(status.lastUpdate).toLocaleString()}`);
    
    console.log('\n📁 FILE HASH:');
    Object.entries(status.fileHashes).forEach(([filename, hash]) => {
        console.log(`   ${filename}: ${hash}`);
    });
    
} else {
    console.log('❌ ERRORE DURANTE CANCELLAZIONE CACHE');
}

console.log('\n🔗 LINK UTILI:');
console.log('   Dashboard: http://localhost:8080/');
console.log('   Dashboard: http://localhost:8080/dashboard.html');
console.log('   Cache Control: http://localhost:8080/cache_control.html');
console.log('   API Status: http://localhost:8080/api/cache/status');
console.log('\n💡 ISTRUZIONI:');
console.log('   1. Riavvia il server: node server.js');
console.log('   2. Vai alla dashboard');
console.log('   3. Apri Console (F12) per vedere i log');
console.log('   4. Ogni modifica ai file JS sarà automaticamente rilevata!');
