#!/usr/bin/env node

/**
 * 🔒 SCRIPT SETUP SICUREZZA VIDEOCHAT ELITE
 * Configura automaticamente le credenziali sicure
 */

const fs = require('fs');
const crypto = require('crypto');
const path = require('path');

console.log('🔒 VIDEOCHAT ELITE - SETUP SICUREZZA');
console.log('=====================================');

// Genera segreto sicuro
function generateSecureSecret(length = 64) {
    return crypto.randomBytes(length).toString('hex');
}

// Controlla se .env esiste
const envPath = path.join(__dirname, '.env');
const envExamplePath = path.join(__dirname, '.env.example');

if (fs.existsSync(envPath)) {
    console.log('✅ File .env già esistente');
    
    // Controlla se TURN_SECRET è configurato
    const envContent = fs.readFileSync(envPath, 'utf8');
    if (!envContent.includes('TURN_SECRET=') || envContent.includes('TURN_SECRET=your_secure_turn_secret_here')) {
        console.log('🔑 Generazione TURN_SECRET...');
        
        const turnSecret = generateSecureSecret(32);
        const updatedContent = envContent.replace(
            /TURN_SECRET=.*/,
            `TURN_SECRET=${turnSecret}`
        );
        
        fs.writeFileSync(envPath, updatedContent);
        console.log('✅ TURN_SECRET aggiornato nel file .env');
    } else {
        console.log('✅ TURN_SECRET già configurato');
    }
} else {
    console.log('📝 Creazione file .env da template...');
    
    if (!fs.existsSync(envExamplePath)) {
        console.error('❌ File .env.example non trovato!');
        process.exit(1);
    }
    
    // Copia template e genera segreti
    let envContent = fs.readFileSync(envExamplePath, 'utf8');
    
    // Genera segreti sicuri
    const turnSecret = generateSecureSecret(32);
    const jwtSecret = generateSecureSecret(32);
    const sessionSecret = generateSecureSecret(32);
    
    // Sostituisci placeholder
    envContent = envContent.replace('your_secure_turn_secret_here_64_characters_minimum', turnSecret);
    envContent = envContent.replace('your_jwt_secret_here', jwtSecret);
    envContent = envContent.replace('your_session_secret_here', sessionSecret);
    
    fs.writeFileSync(envPath, envContent);
    console.log('✅ File .env creato con segreti sicuri');
}

// Verifica configurazione
console.log('\n🔍 VERIFICA CONFIGURAZIONE:');

try {
    require('dotenv').config();
    
    const checks = [
        { name: 'TURN_SECRET', value: process.env.TURN_SECRET, required: true },
        { name: 'MONGO_URI', value: process.env.MONGO_URI, required: true },
        { name: 'REDIS_URI', value: process.env.REDIS_URI, required: true },
        { name: 'TURN_SERVER_1', value: process.env.TURN_SERVER_1, required: false },
        { name: 'TURN_SERVER_2', value: process.env.TURN_SERVER_2, required: false }
    ];
    
    let allGood = true;
    
    checks.forEach(check => {
        if (check.value && check.value !== `your_${check.name.toLowerCase()}_here`) {
            console.log(`✅ ${check.name}: Configurato`);
        } else if (check.required) {
            console.log(`❌ ${check.name}: NON CONFIGURATO (richiesto)`);
            allGood = false;
        } else {
            console.log(`⚠️ ${check.name}: Non configurato (opzionale)`);
        }
    });
    
    if (allGood) {
        console.log('\n🎉 CONFIGURAZIONE SICUREZZA COMPLETATA!');
        console.log('✅ Tutte le credenziali sono configurate correttamente');
        console.log('🔒 Le credenziali TURN sono ora sicure e temporanee');
        console.log('🚀 Puoi avviare il server con: node server.js');
    } else {
        console.log('\n⚠️ CONFIGURAZIONE INCOMPLETA');
        console.log('📝 Modifica il file .env per completare la configurazione');
    }
    
} catch (error) {
    console.error('❌ Errore verifica configurazione:', error.message);
}

console.log('\n🔗 PROSSIMI PASSI:');
console.log('1. Modifica .env con le tue credenziali database');
console.log('2. Configura i server TURN se diversi da quelli default');
console.log('3. Avvia il server: node server.js');
console.log('4. Le credenziali TURN saranno generate automaticamente!');
