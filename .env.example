# 🔒 CONFIGURAZIONE SICUREZZA VIDEOCHAT ELITE
# Copia questo file in .env e configura i valori

# 🔐 CREDENZIALI TURN SICURE
# Genera un segreto sicuro di 64 caratteri per firmare i token TURN
# Esempio: openssl rand -hex 32
TURN_SECRET=your_secure_turn_secret_here_64_characters_minimum

# 🌐 SERVER TURN
# Configura i tuoi server TURN
TURN_SERVER_1=turn:videochatcouple.com:3478
TURN_SERVER_2=turns:videochatcouple.com:5349

# 🗄️ DATABASE
# MongoDB connection string
MONGO_URI=mongodb://localhost:27017/videochat_elite

# 🔴 REDIS
# Redis connection string per Socket.IO clustering
REDIS_URI=redis://localhost:6379

# 🚀 SERVER
# Porta del server (default: 8080)
PORT=8080

# 🌍 AMBIENTE
# Ambiente di esecuzione (development/production)
NODE_ENV=development

# 🔒 SICUREZZA AGGIUNTIVA
# Chiavi per JWT, sessioni, etc.
JWT_SECRET=your_jwt_secret_here
SESSION_SECRET=your_session_secret_here

# 📧 EMAIL (per notifiche admin)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password

# 🔍 MONITORING
# Sentry DSN per error tracking (opzionale)
SENTRY_DSN=https://your_sentry_dsn_here

# 📊 ANALYTICS
# Google Analytics ID (opzionale)
GA_TRACKING_ID=UA-XXXXXXXXX-X
