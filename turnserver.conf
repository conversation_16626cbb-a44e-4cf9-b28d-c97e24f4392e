# =================================================================
# == File di Configurazione per COTURN - Soluzione A (Robusta) ==
# =================================================================

# --- Indirizzi e Porte ---
external-ip=************
listening-port=3478

# TURN/TLS solo sulla porta 5349 (standard) per evitare conflitti con Nginx
tls-listening-port=5349

# Range porte per il traffico multimediale (consigliato: fascia ristretta)
min-port=49152
max-port=49999

# --- Sicurezza e Autenticazione ---
realm=videochatcouple.com
# Usa credenziali temporanee firmate (REST API compatibile)
use-auth-secret
lt-cred-mech
# ATTENZIONE: inserisci qui lo stesso segreto configurato in .env (TURN_SECRET)
# Esempio: static-auth-secret=ab12cd34ef56...
static-auth-secret=7f9a3c12b5e84d0fa2c1e3b49758d6c0f1a2b3c4d5e6f7089abcdeff00112233

# Certificati TLS
cert=/etc/letsencrypt/live/videochatcouple.com/fullchain.pem
pkey=/etc/letsencrypt/live/videochatcouple.com/privkey.pem

# --- Ottimizzazioni e Logging ---
fingerprint
stale-nonce
no-loopback-peers
no-multicast-peers
no-rfc5780
no-stun-backward-compatibility
syslog
verbose