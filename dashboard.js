let socket, peerConnection, localStream, partnerId;
let socketInitialized = false;
let socketReadyPromise = null;
let cameraActive = false, audioActive = true, isSearching = false, isInCall = false, explicitContentRequester = null;
let explicitActive = false; // stato condiviso durante la chiamata
let explicitPending = false; // richiesta in corso

// 🚀 SISTEMA ESCALATION ULTRA-INTELLIGENTE
let escalationManager = null;
let connectionAttempts = 0;
let isInitiator = false;
let makingOffer = false;
let isPolite = false;
let ignoreOffer = false;
// --- Notifiche semplici (fallback) ---
function showNotification(message, type = 'info') {
    const c = document.getElementById('notification-container');
    if (!c) { alert(message); return; }
    const n = document.createElement('div');
    n.className = `notif ${type}`;
    n.textContent = message;
    c.appendChild(n);
    setTimeout(() => { n.classList.add('in'); }, 10);
    setTimeout(() => { n.classList.remove('in'); n.remove(); }, 3000);
}


// Telemetry counters for debugging production issues (glare, queues, etc.)
let metrics = {
    offersReceived: 0,
    glareCount: 0,
    ignoredOffers: 0,
    iceCandidatesQueued: 0
};
// --- Modal helpers (global) ---
function showModal(id) {
    const el = document.getElementById(id);
    if (el) el.style.display = 'block';
}
function closeModal(id) {
    const el = document.getElementById(id);
    if (el) el.style.display = 'none';
}

// --- Explicit content helpers: UX professionale (global) ---
function setExplicitIdle() {
    const btn = document.getElementById('explicitBtn'); if (!btn) return;
    btn.disabled = !isInCall;
    btn.classList.remove('pulse');
    btn.classList.remove('explicit-on');
    btn.setAttribute('title', 'Contenuti espliciti');
    btn.innerHTML = '<svg class="btn-icon" viewBox="0 0 24 24" aria-hidden="true"><title>Explicit</title><path d="M12 2c2 3 3 4 3 6 0 2-1.2 3.5-3 4-1.5-.5-3-2-3-4 0-1.5.7-2.6 1.6-3.7C9.1 5 8 6.5 8 9c0 3.9 3 7 4 9 1-2 4-5.1 4-9 0-3-1.4-5-4-7z"/></svg>';
}
function setExplicitPending() {
function setExplicitOn() {
    const btn = document.getElementById('explicitBtn'); if (!btn) return;
    btn.disabled = true;
    btn.classList.remove('pulse');
    btn.classList.add('explicit-on');
    btn.setAttribute('title', 'Contenuti espliciti attivi');
    btn.innerHTML = '<svg class="btn-icon" viewBox="0 0 24 24" aria-hidden="true"><title>Explicit ON</title><path d="M12 2c2 3 3 4 3 6 0 2-1.2 3.5-3 4-1.5-.5-3-2-3-4 0-1.5.7-2.6 1.6-3.7C9.1 5 8 6.5 8 9c0 3.9 3 7 4 9 1-2 4-5.1 4-9 0-3-1.4-5-4-7z"/></svg>';
}

    const btn = document.getElementById('explicitBtn'); if (!btn) return;
    explicitPending = true;
    btn.disabled = true;
    btn.classList.add('pulse');
    btn.setAttribute('title', 'Richiesta inviata...');
}
function setExplicitRequested() {
    const btn = document.getElementById('explicitBtn'); if (!btn) return;
    btn.disabled = false;
    btn.classList.add('pulse');
    btn.setAttribute('title', 'Richiesta in arrivo');
}
function requestExplicitContent() {
    if (!isInCall || !socket || explicitActive || explicitPending) return;
    try {
        setExplicitPending();
        socket.emit('explicit-content-request', { to: partnerId });
        showNotification('Richiesta inviata', 'info');
    } catch(e) { console.warn('explicit request failed', e); explicitPending = false; setExplicitIdle(); }
}
function acceptExplicitContent() {
    if (!isInCall || !socket) return;
    try {
        socket.emit('explicit-content-accept', { to: explicitContentRequester || partnerId });
        // lato client: attiva subito
        explicitActive = true; explicitPending = false;
        setExplicitOn();
        closeModal('explicitModal');
        showNotification('Contenuti espliciti abilitati', 'success');
    } catch(e) { console.warn('explicit accept failed', e); }
}
function rejectExplicitContent() {
    if (!isInCall || !socket) return;
    try {
        socket.emit('explicit-content-reject', { to: explicitContentRequester || partnerId });
        explicitPending = false;
        showNotification('Richiesta rifiutata', 'info');
        closeModal('explicitModal');
        setExplicitIdle();
    } catch(e) { console.warn('explicit reject failed', e); }
}

// Esponi globalmente subito
window.requestExplicitContent = requestExplicitContent;
window.acceptExplicitContent = acceptExplicitContent;
window.rejectExplicitContent = rejectExplicitContent;


// 🔒 SISTEMA CREDENZIALI TURN SICURE
let currentTurnCredentials = null;
let turnTokenId = null;
let userId = null;
// Advanced runtime controls for compatibility and adaptation
let statsMonitorInterval = null;
let lastStats = {};
const iceCandidateQueue = new Map(); // map partnerId -> [candidates]

// Helper: queue ICE candidates that arrive before PeerConnection is ready
function queueIceCandidate(from, candidate) {
    if (!iceCandidateQueue.has(from)) iceCandidateQueue.set(from, []);
    iceCandidateQueue.get(from).push(candidate);
    metrics.iceCandidatesQueued = (metrics.iceCandidatesQueued || 0) + 1;
}

// Reliable ICE candidate sender with small retry
async function sendIceCandidateReliable(candidate, to) {
    const payload = { candidate, to };
    const maxAttempts = 3;
    for (let attempt = 0; attempt < maxAttempts; attempt++) {
        try {
            if (socket && socket.emit) {
                socket.emit('ice-candidate', payload);
                return;
            }
        } catch (err) {
            console.warn('emit ice-candidate failed, attempt', attempt + 1, err);
        }
        await new Promise(r => setTimeout(r, 250 * (attempt + 1)));
    }
    queueIceCandidate(to || 'pending', candidate);
}

function processQueuedIceCandidates(forPartnerId) {
    if (!forPartnerId) return;
    const queued = iceCandidateQueue.get(forPartnerId) || [];
    queued.forEach(async c => {
        try {
            if (peerConnection) await peerConnection.addIceCandidate(new RTCIceCandidate(c));
            console.log('✅ ICE candidate from queue added');
            metrics.iceCandidatesQueued = Math.max(0, (metrics.iceCandidatesQueued || 0) - 1);
        } catch (e) {
            console.warn('Failed to add queued ICE candidate', e);
        }
    });
    iceCandidateQueue.delete(forPartnerId);
}

// Adaptive quality: attempt to set codec preferences (prefer H264 on iOS/Safari)
function preferCodec(pc, kind = 'video', preferred = 'H264') {
    try {
        if (!pc || !pc.getTransceivers) return;
        const capabilities = RTCRtpSender.getCapabilities ? RTCRtpSender.getCapabilities(kind) : null;
        if (!capabilities || !capabilities.codecs) return;
        const codecs = capabilities.codecs;
        const preferredCodecs = codecs.filter(c => c.mimeType && c.mimeType.toLowerCase().includes(preferred.toLowerCase()));
        if (preferredCodecs.length === 0) return;
        pc.getTransceivers().forEach(transceiver => {
            if (transceiver.receiver && transceiver.receiver.track && transceiver.receiver.track.kind === kind) {
                try {
                    const newPrefs = [...preferredCodecs, ...codecs.filter(c => !preferredCodecs.includes(c))];
                    transceiver.setCodecPreferences(newPrefs);
                    console.log('✅ Codec preferences set to prefer', preferred);
                } catch (e) {
                    // ignore if not supported
                }
            }
        });
    } catch (e) {
        console.warn('preferCodec error', e);
    }
}

// Adjust sender parameters (bitrate/frameRate) for video
async function applyVideoSenderParams(pc, opts = {}) {
    try {
        if (!pc) return;
        const sender = pc.getSenders().find(s => s.track && s.track.kind === 'video');
        if (!sender || !sender.getParameters) return;
        const params = sender.getParameters();
        if (!params.encodings) params.encodings = [{}];
        // Apply suggested maxBitrate and maxFramerate
        if (typeof opts.maxBitrate === 'number') params.encodings[0].maxBitrate = opts.maxBitrate;
        if (typeof opts.maxFramerate === 'number') params.encodings[0].maxFramerate = opts.maxFramerate;
        // set scalabilityMode when available
        if (opts.scalabilityMode) params.encodings[0].scalabilityMode = opts.scalabilityMode;
        await sender.setParameters(params);
        console.log('✅ Applied video sender params', params.encodings[0]);
    } catch (e) {
        console.warn('applyVideoSenderParams failed', e);
    }
}

// Monitor stats and adapt quality where needed
function startStatsMonitor(pc) {
    stopStatsMonitor();
    if (!pc || !pc.getStats) return;
    statsMonitorInterval = setInterval(async () => {
        try {
            const stats = await pc.getStats(null);
            let rtt = 0, packetsLost = 0, packetsSent = 0, framesEncoded = 0;
            stats.forEach(report => {
                if (report.type === 'outbound-rtp' && report.kind === 'video') {
                    packetsLost = report.packetsLost || packetsLost;
                    packetsSent = report.packetsSent || packetsSent;
                    framesEncoded = report.framesEncoded || framesEncoded;
                }
                if (report.type === 'candidate-pair' && report.currentRoundTripTime) {
                    rtt = report.currentRoundTripTime * 1000; // sec -> ms
                }
            });

            // Basic adaptation logic
            if (rtt > 300 || packetsLost > 100) {
                // poor network: reduce bitrate/frameRate
                await applyVideoSenderParams(pc, { maxBitrate: 200_000, maxFramerate: 15 });
                // try to reduce camera constraints if possible
                if (localStream) {
                    const videoTrack = localStream.getVideoTracks()[0];
                    if (videoTrack && videoTrack.applyConstraints) {
                        try {
                            await videoTrack.applyConstraints({ frameRate: { max: 15 }, width: { max: 640 }, height: { max: 480 } });
                            console.log('✅ Applied reduced constraints for poor network');
                        } catch (err) {}
                    }
                }
            } else if (rtt > 150 || packetsLost > 20) {
                await applyVideoSenderParams(pc, { maxBitrate: 500_000, maxFramerate: 20 });
            } else {
                await applyVideoSenderParams(pc, { maxBitrate: 1500000, maxFramerate: 30 });
            }
            lastStats = { rtt, packetsLost, packetsSent, framesEncoded };
        } catch (e) {
            console.warn('stats monitor error', e);
        }
    }, 3000);
}

function stopStatsMonitor() {
    if (statsMonitorInterval) { clearInterval(statsMonitorInterval); statsMonitorInterval = null; }
}

function generateUserId() {
    if (!userId) userId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    return userId;
}

function isMobileDevice() {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}

// 🔒 OTTIENI CREDENZIALI TURN SICURE DAL SERVER
async function getSecureTurnCredentials() {
    try {
        const resp = await fetch('/api/turn/credentials', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ userId: generateUserId() })
        });
        if (!resp.ok) throw new Error(`HTTP ${resp.status}`);
        const data = await resp.json();
        if (!data.success) throw new Error(data.error || 'TURN credentials failed');
        currentTurnCredentials = data.iceServers;
        turnTokenId = data.tokenId;
        const renewMs = data.expiresAt - Date.now() - (2 * 60 * 60 * 1000);
        if (renewMs > 0) setTimeout(renewTurnCredentials, renewMs);
        console.log('✅ TURN credentials obtained');
        return currentTurnCredentials;
    } catch (e) {
        console.error('TURN credentials error:', e);
        // Fallback STUN-only
        return [
            { urls: 'stun:stun.l.google.com:19302' },
            { urls: 'stun:stun1.l.google.com:19302' },
            { urls: 'stun:stun2.l.google.com:19302' },
            { urls: 'stun:stun3.l.google.com:19302' },
            { urls: 'stun:stun4.l.google.com:19302' }
        ];
    }
}

async function renewTurnCredentials() {
    try {
        const resp = await fetch('/api/turn/renew', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ tokenId: turnTokenId, userId: generateUserId() })
        });
        if (!resp.ok) throw new Error(`HTTP ${resp.status}`);
        const data = await resp.json();
        if (!data.success) throw new Error(data.error || 'TURN renew failed');
        currentTurnCredentials = data.iceServers;
        turnTokenId = data.tokenId || turnTokenId;
        const renewMs = data.expiresAt - Date.now() - (2 * 60 * 60 * 1000);
        if (renewMs > 0) setTimeout(renewTurnCredentials, renewMs);
        console.log('✅ TURN credentials renewed');
        return currentTurnCredentials;
    } catch (e) {
        console.error('TURN renew error:', e);
        return getSecureTurnCredentials();
    }
}

function getVideoConstraints() {
    // Usa la configurazione ottimizzata se disponibile
    if (typeof window.MobileWebRTCConfig !== 'undefined') {
        return window.MobileWebRTCConfig.getOptimizedMediaConstraints();
    }

    // Fallback alla configurazione originale
    if (isMobileDevice()) {
        return {
            video: {
                width: { ideal: 640 },
                height: { ideal: 480 },
                frameRate: { ideal: 24, max: 30 },
                facingMode: 'user'
            },
            audio: { echoCancellation: true, noiseSuppression: true, autoGainControl: true }
        };
    } else {
        return {
            video: { width: { ideal: 1280 }, height: { ideal: 720 }, frameRate: { ideal: 30 }},
            audio: true
        };
    }
}

// Helper per aggiornare icone SVG dei pulsanti (sostituisce Font Awesome)
function setCameraIcon(active) {
    const btn = document.getElementById('cameraBtn');
    if (!btn) return;
    if (active) {
        btn.innerHTML = '<svg width="20" height="20" viewBox="0 0 24 24" aria-hidden="true"><title>Camera Off</title><rect x="3" y="6" width="14" height="11" rx="2" ry="2" stroke="currentColor" fill="none"/><circle cx="12" cy="11.5" r="3" fill="currentColor"/><line x1="3" y1="3" x2="21" y2="21" stroke="currentColor" stroke-width="1.5"/></svg>';
    } else {
        btn.innerHTML = '<svg width="20" height="20" viewBox="0 0 24 24" aria-hidden="true"><title>Camera</title><rect x="3" y="6" width="14" height="11" rx="2" ry="2" stroke="currentColor" fill="none"/><circle cx="12" cy="11.5" r="3" fill="currentColor"/></svg>';
    }
}

function setAudioIcon(active) {
    const btn = document.getElementById('audioBtn');
    if (!btn) return;
    if (active) {
        btn.innerHTML = '<svg width="20" height="20" viewBox="0 0 24 24" aria-hidden="true"><title>Mic</title><rect x="10" y="3" width="4" height="10" rx="2" fill="currentColor"/><path d="M5 11v1a7 7 0 0 0 14 0v-1" stroke="currentColor" fill="none"/></svg>';
    } else {
        btn.innerHTML = '<svg width="20" height="20" viewBox="0 0 24 24" aria-hidden="true"><title>Mic Off</title><rect x="10" y="3" width="4" height="10" rx="2" fill="currentColor"/><path d="M5 11v1a7 7 0 0 0 14 0v-1" stroke="currentColor" fill="none"/><line x1="3" y1="3" x2="21" y2="21" stroke="currentColor" stroke-width="1.5"/></svg>';
    }
}


async function startLocalStream(tries = 0) {
    if (localStream) {
        localStream.getTracks().forEach(track => track.stop());
    }

    try {
        const constraints = getVideoConstraints();
        console.log("🎥 Richiesta getUserMedia con constraints:", constraints);

        // 🍎 FIX SPECIFICO PER iOS
        const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
        if (isIOS) {
            console.log("🍎 Dispositivo iOS rilevato - applicando fix specifici");

            // iOS richiede constraints più semplici
            const iosConstraints = {
                video: {
                    facingMode: 'user',
                    width: { ideal: 640 },
                    height: { ideal: 480 }
                },
                audio: true
            };

            console.log("🍎 Usando constraints iOS:", iosConstraints);
            localStream = await navigator.mediaDevices.getUserMedia(iosConstraints);
        } else {
            localStream = await navigator.mediaDevices.getUserMedia(constraints);
        }

        const videoElem = document.getElementById("localVideo");
        videoElem.srcObject = localStream;

        // 🍎 Attributi specifici per iOS
        if (isIOS) {
            videoElem.setAttribute('playsinline', 'true');
            videoElem.setAttribute('webkit-playsinline', 'true');
            videoElem.muted = true; // Importante per iOS
        }

        videoElem.style.display = "block";
        document.getElementById("localPlaceholder").style.display = "none";
    setCameraIcon(true);
        cameraActive = true;

        console.log("✅ Stream locale avviato con successo");
        return true;

    } catch (err) {
        console.error("❌ Errore getUserMedia:", err);
        // If initial attempt failed, try lower constraints once or twice
        if (tries < 2) {
            console.log(`Tentativo getUserMedia fallback #${tries + 1}`);
            // reduce constraints
            const fallbackConstraints = isMobileDevice() ?
                { video: { width: { ideal: 480 }, height: { ideal: 360 }, frameRate: { ideal: 15 } }, audio: true } :
                { video: { width: { ideal: 640 }, height: { ideal: 360 }, frameRate: { ideal: 20 } }, audio: true };
            try {
                localStream = await navigator.mediaDevices.getUserMedia(fallbackConstraints);
                const videoElem = document.getElementById("localVideo");
                videoElem.srcObject = localStream;
                videoElem.style.display = "block";
                document.getElementById("localPlaceholder").style.display = "none";
                setCameraIcon(true);
                cameraActive = true;
                console.log('✅ Stream locale avviato con constraints fallback');
                return true;
            } catch (err2) {

                console.warn('Fallback getUserMedia fallito', err2);
                return startLocalStream(tries + 1);
            }
        }

        // 🍎 Messaggi di errore specifici per iOS
        const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
        if (isIOS) {
            if (err.name === 'NotAllowedError') {
                showNotification("Permessi camera negati. Vai in Impostazioni > Safari > Camera", "error");
            } else if (err.name === 'NotFoundError') {
                showNotification("Camera non trovata. Verifica che non sia usata da altre app", "error");
            } else {
                showNotification(`Errore camera iOS: ${err.message}`, "error");
            }
        } else {
            showNotification("Videocamera non accessibile.", "error");
        }

        cameraActive = false;
    setCameraIcon(false);
        return false;
    }
}

function stopLocalStream() {
    if (localStream) {
        localStream.getTracks().forEach(track => track.stop());
        localStream = null;
    }
    const videoElem = document.getElementById("localVideo");
    videoElem.srcObject = null;
    videoElem.style.display = "none";
    document.getElementById("localPlaceholder").style.display = "block";
    setCameraIcon(false);
    cameraActive = false;
}

async function toggleCamera() {
    if (cameraActive) {
        stopLocalStream();
        setCameraIcon(false);
    } else {
        await startLocalStream();
        setCameraIcon(true);
    }
}

function toggleAudio() {
    audioActive = !audioActive;
    setAudioIcon(audioActive);
    if (localStream) {
        localStream.getAudioTracks().forEach(track => track.enabled = audioActive);
    }
}

function updateCallControls() {
    const isBusy = isInCall || isSearching;
    document.getElementById("matchBtn").disabled = isBusy;
    document.getElementById("skipBtn").disabled = !isInCall;
    document.getElementById("endCallBtn").disabled = !isInCall;
    ["explicitBtn", "reportBtn", "chatInput", "chatSendBtn"].forEach(id => document.getElementById(id).disabled = !isInCall);
}

function initializeSocket() {
    socket = io("/");
    // Return a promise that resolves when socket is connected
    if (!socketReadyPromise) {
        socketReadyPromise = new Promise(resolve => {
            socket.on("connect", () => {
                console.log("Connesso al server Socket.IO");
                socketInitialized = true;
                resolve(socket);
            });
        });

        socket.on("update-user-count", e => {
// --- Icon helpers for muted/off state (spostate fuori dal blocco try/catch) ---
function setCameraIcon(on) {
    const btn = document.getElementById("cameraBtn");
    if (!btn) return;
    if (on) {
        btn.classList.remove('btn-red');
        btn.classList.add('btn-gray');
        btn.setAttribute('title','Fotocamera attiva');
        btn.innerHTML = '<svg class="btn-icon" viewBox="0 0 24 24" aria-hidden="true"><title>Camera</title><rect x="3" y="6" width="14" height="11" rx="2" ry="2" stroke="#fff" fill="none"/><circle cx="12" cy="11.5" r="3" fill="#fff"/></svg>';
    } else {
        btn.classList.remove('btn-gray');
        btn.classList.add('btn-red');
        btn.setAttribute('title','Fotocamera spenta');
        btn.innerHTML = '<svg class="btn-icon" viewBox="0 0 24 24" aria-hidden="true"><title>Camera off</title><rect x="3" y="6" width="14" height="11" rx="2" ry="2" stroke="#fff" fill="none"/><circle cx="12" cy="11.5" r="3" fill="#fff"/><line x1="4" y1="4" x2="20" y2="20" stroke="#fff" stroke-width="2"/></svg>';
    }
}

function setAudioIcon(on) {
    const btn = document.getElementById("audioBtn");
    if (!btn) return;
    if (on) {
        btn.classList.remove('btn-red');
        btn.classList.add('btn-gray');
        btn.setAttribute('title','Microfono attivo');
        btn.innerHTML = '<svg class="btn-icon" viewBox="0 0 24 24" aria-hidden="true"><title>Mic</title><rect x="10" y="3" width="4" height="10" rx="2" fill="#fff"/><path d="M5 11v1a7 7 0 0 0 14 0v-1" stroke="#fff" fill="none"/></svg>';
    } else {
        btn.classList.remove('btn-gray');
        btn.classList.add('btn-red');
        btn.setAttribute('title','Microfono disattivato');
        btn.innerHTML = '<svg class="btn-icon" viewBox="0 0 24 24" aria-hidden="true"><title>Mic off</title><rect x="10" y="3" width="4" height="10" rx="2" fill="#fff"/><path d="M5 11v1a7 7 0 0 0 14 0v-1" stroke="#fff" fill="none"/><line x1="4" y1="4" x2="20" y2="20" stroke="#fff" stroke-width="2"/></svg>';
    }
}

        const userCountElem = document.getElementById("userCount");
        if (userCountElem && typeof e.count !== 'undefined') userCountElem.textContent = e.count;
    });
    } else {
        // If already have a ready promise, still attach handlers immediately
        socket.on("connect", () => console.log("Connesso al server Socket.IO"));
    }

    socket.on("match-found", handleMatchFound);
    socket.on('match-proposal', handleMatchProposal);
    socket.on("offer", handleOffer);
    socket.on("answer", handleAnswer);
    socket.on("ice-candidate", handleIceCandidate);
    socket.on('disconnect', (reason) => {
        console.log('Socket disconnected:', reason);
        socketInitialized = false;
        socketReadyPromise = null;
    });
    socket.on("call-ended-by-partner", () => {
        cleanUpCall("Partner disconnesso. Cerco un nuovo partner...");
        setTimeout(findMatch, 200);
    });
    socket.on("chat-message", e => addChatMessage("Partner", e.message, "partner"));

    // ==========================================================
    // FUNZIONALITÀ RIPRISTINATE
    // ==========================================================
    socket.on("explicit-content-request", e => {
        if (e.from && !explicitActive) {
            explicitContentRequester = e.from;
            setExplicitRequested();
            showModal("explicitModal");
        }
    });
    socket.on("explicit-content-accept", e => {
        // partner ha accettato: attiva espliciti e blocca ulteriori richieste
        explicitActive = true; explicitPending = false;
        setExplicitOn();
        closeModal('explicitModal');
        showNotification('Contenuti espliciti abilitati per entrambi', 'success');
    });
    socket.on("explicit-content-reject", e => {
        explicitPending = false;
        setExplicitIdle();
        closeModal('explicitModal');
        showNotification('Richiesta espliciti rifiutata - passo al prossimo partner', 'info');
        // passa direttamente al prossimo per entrambi i client
        setTimeout(() => { try { skipUser(); } catch(_){} }, 50);
    });


    socket.on("dsa-report-submitted", e => {
        closeDsaReportModal();
        showNotification(e.message, "success");
        document.getElementById("submitDsaReportBtn").disabled = false;
        skipUser("Segnalazione inviata. Cerco un nuovo partner...");
    });

    socket.on("dsa-report-error", e => {
        showNotification(e.message, "error");
        document.getElementById("submitDsaReportBtn").disabled = false;
    });
    // ==========================================================

    socket.on('error', message => showNotification(message, 'error'));
    // Periodically flush metrics to server if available
    if (!window.__metricsFlusher) {
        window.__metricsFlusher = setInterval(() => {
            if (socket && socket.emit) {
                socket.emit('client-metrics', { metrics });
            }
        }, 15000);
    }

    return socketReadyPromise || Promise.resolve(socket);
}

// Utility to dynamically load external script (used to lazy-load Socket.IO client)
function loadExternalScript(url, attrs = {}) {
    return new Promise((resolve, reject) => {
        const existing = document.querySelector(`script[src="${url}"]`);
        if (existing) {
            existing.addEventListener('load', () => resolve(existing));
            existing.addEventListener('error', () => reject(new Error('Failed to load script')));
            return;
        }
        const s = document.createElement('script');
        s.src = url;
        s.async = true;
        Object.keys(attrs).forEach(k => s.setAttribute(k, attrs[k]));
        let settled = false;
        const timer = setTimeout(() => {
            if (!settled) {
                settled = true;
                s.remove();
                reject(new Error(`Timeout loading ${url}`));
            }
        }, 10000);

        s.onload = () => {
            if (!settled) {
                settled = true;
                clearTimeout(timer);
                resolve(s);
            }
        };
        s.onerror = () => {
            if (!settled) {
                settled = true;
                clearTimeout(timer);
                reject(new Error(`Failed to load ${url}`));
            }
        };
        document.head.appendChild(s);
    });
}

async function createPeerConnection(strategy = 'adaptive') {
    try {
        // 🔒 Ottieni ICE servers sicuri
        const secureIceServers = await getSecureTurnCredentials();

        // 🚀 SISTEMA ULTRA-INTELLIGENTE DI CONFIGURAZIONE
        let config;
        if (typeof window.MobileWebRTCConfig === 'undefined') {
            // Try to lazy-load mobile_webrtc_config.js from the same origin
            try {
                await loadExternalScript('mobile_webrtc_config.js');
                console.log('✅ mobile_webrtc_config.js caricato dinamicamente');
            } catch (err) {
                console.warn('Impossibile caricare mobile_webrtc_config.js dinamicamente:', err);
            }
        }

        if (typeof window.MobileWebRTCConfig !== 'undefined') {
            if (!escalationManager) {
                escalationManager = new window.MobileWebRTCConfig.ConnectionEscalationManager();
            }
            if (strategy === 'adaptive') {
                config = escalationManager.startConnection(null, handleConnectionEscalation);
            } else {
                config = window.MobileWebRTCConfig.getSmartIceConfig(strategy);
            }
            // Inserisci i server sicuri nella config
            config.iceServers = secureIceServers;
        } else {
            config = {
                iceServers: secureIceServers,
                iceGatheringTimeout: 20000,
                iceTransportPolicy: 'all'
            };
        }

        peerConnection = new RTCPeerConnection(config);

        // Aggiungi tracce locali se disponibili
        if (localStream) {
            localStream.getTracks().forEach(track => {
                console.log(`Aggiungendo track: ${track.kind}`);
                peerConnection.addTrack(track, localStream);
            });
        }
        // Prefer codec and apply conservative sender params initially
        try {
            preferCodec(peerConnection, 'video', /iPad|iPhone|iPod/.test(navigator.userAgent) ? 'H264' : 'VP8');
            await applyVideoSenderParams(peerConnection, { maxBitrate: 800000, maxFramerate: 24 });
        } catch (e) {
            // non-blocking if fails
        }

    // Gestione stream remoto con fix iOS
        peerConnection.ontrack = e => {
            console.log("Stream remoto ricevuto");
            const remoteVideo = document.getElementById("remoteVideo");
            remoteVideo.srcObject = e.streams[0];
            remoteVideo.style.display = "block";
            document.getElementById("remotePlaceholder").style.display = "none";

            // 🍎 FIX COMPLETO PER iOS/SAFARI
            const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
            if (isIOS) {
                console.log("🍎 Applicando fix completi iOS");

                // Attributi essenziali per iOS
                remoteVideo.setAttribute('playsinline', 'true');
                remoteVideo.setAttribute('webkit-playsinline', 'true');
                remoteVideo.setAttribute('controls', 'false');
                remoteVideo.muted = false;

                // Forza play con retry per iOS
                const attemptPlay = async () => {
                    try {
                        await remoteVideo.play();
                        console.log("✅ Video remoto in riproduzione su iOS");
                    } catch (error) {
                        console.log("⚠️ Primo tentativo play fallito, riprovo...");

                        // Secondo tentativo con muted
                        remoteVideo.muted = true;
                        try {
                            await remoteVideo.play();
                            console.log("✅ Video remoto in riproduzione (muted) su iOS");

                            // Dopo 1 secondo prova a togliere mute
                            setTimeout(() => {
                                remoteVideo.muted = false;
                            }, 1000);
                        } catch (error2) {
                            console.log("❌ Autoplay completamente bloccato su iOS");
                            showNotification("Tocca il video per attivarlo", "info");

                            // Aggiungi click listener per attivazione manuale
                            remoteVideo.addEventListener('click', () => {
                                remoteVideo.play();
                            }, { once: true });
                        }
                    }
                };

                // Ritarda leggermente per iOS
                setTimeout(attemptPlay, 500);
            }
        };

        // Gestione negoziazione con polite/impolite (glare handling)
        peerConnection.onnegotiationneeded = async () => {
            console.log('onnegotiationneeded fired');
            if (!socketInitialized || !partnerId) {
                console.log('Negoziazione posticipata, socket o partner non pronti');
                return;
            }
            try {
                makingOffer = true;
                const offer = await peerConnection.createOffer();
                await peerConnection.setLocalDescription(offer);
                socket.emit('offer', { offer, to: partnerId });
                console.log('Offer inviato da onnegotiationneeded');
            } catch (err) {
                console.warn('Errore onnegotiationneeded:', err);
                try {
                    const offer = await peerConnection.createOffer({ iceRestart: true });
                    await peerConnection.setLocalDescription(offer);
                    socket.emit('offer', { offer, to: partnerId });
                    console.log('Offer inviato con ICE restart');
                } catch (err2) {
                    console.error('Negoziazione fallita completamente:', err2);
                }
            } finally {
                makingOffer = false;
            }
        };

        // Gestione ICE candidates con retry logic
        peerConnection.onicecandidate = e => {
            if (e.candidate) {
                console.log(`ICE Candidate: ${e.candidate.type} - ${e.candidate.protocol}`);
                // If socket is not initialized or partnerId missing, queue locally to retry later
                if (!socketInitialized || !partnerId) {
                    queueIceCandidate(partnerId || 'pending', e.candidate);
                } else {
                    sendIceCandidateReliable(e.candidate, partnerId);
                }
            } else {
                console.log("ICE gathering completato");
            }
        };

        // Gestione stati connessione migliorata
        peerConnection.onconnectionstatechange = () => {
            const state = peerConnection.connectionState;
            console.log(`Stato connessione WebRTC: ${state}`);

            switch(state) {
                case 'connected':
                case 'completed':
                    console.log("✅ Connessione WebRTC stabilita");
                    isInCall = true;
                    isSearching = false;
                    updateCallControls();
                    showNotification("Connesso!", "success");
                    // Prefer codec for better compatibility (H264 on iOS/Safari)
                    preferCodec(peerConnection, 'video', /iPad|iPhone|iPod/.test(navigator.userAgent) ? 'H264' : 'VP8');
                    // Start stats monitor to adapt quality dynamically
                    startStatsMonitor(peerConnection);
                    // Process any queued ICE candidates for this partner
                    processQueuedIceCandidates(partnerId);
                    break;

                case 'connecting':
                    console.log("🔄 Connessione in corso...");
                    showNotification("Connessione in corso...", "info");
                    break;

                case 'failed':
                    console.log("❌ Connessione fallita - tentativo di riconnessione");
                    handleConnectionFailure();
                    break;

                case 'disconnected':
                    console.log("⚠️ Connessione persa");
                    cleanUpCall("Connessione persa. Cerco un nuovo partner...");
                    setTimeout(findMatch, 300);
                    break;

                case 'closed':
                    console.log("🔒 Connessione chiusa");
                        stopStatsMonitor();
                    break;
            }
        };

        // Gestione ICE connection state per debug
        peerConnection.oniceconnectionstatechange = () => {
            console.log(`ICE Connection State: ${peerConnection.iceConnectionState}`);
        };

        // Gestione ICE gathering state
        peerConnection.onicegatheringstatechange = () => {
            console.log(`ICE Gathering State: ${peerConnection.iceGatheringState}`);
        };

        // 🚀 ATTIVA SISTEMA ESCALATION INTELLIGENTE
        if (escalationManager && strategy === 'adaptive') {
            escalationManager.monitorConnection(peerConnection);
        }

        // Avvia monitoraggio qualità se disponibile
        if (typeof window.MobileWebRTCConfig !== 'undefined') {
            setTimeout(() => {
                if (peerConnection && peerConnection.connectionState === 'connected') {
                    window.MobileWebRTCConfig.monitorConnectionQuality(peerConnection);
                }
            }, 5000);
        }

    } catch (error) {
        console.error("Errore creazione PeerConnection:", error);
        showNotification("Errore inizializzazione connessione", "error");
        throw error;
    }
}

// 🔄 GESTIONE ESCALATION AUTOMATICA ULTRA-INTELLIGENTE
async function handleConnectionEscalation(nextStrategy, attemptNumber) {
    console.log(`🔥 ESCALATION AUTOMATICA: Tentativo ${attemptNumber} con strategia ${nextStrategy.toUpperCase()}`);

    showNotification(`Ottimizzazione connessione... (${attemptNumber}/4)`, "info");

    try {
        // Chiudi connessione corrente
        if (peerConnection) {
            peerConnection.close();
            peerConnection = null;
        }

        // Attendi un momento per cleanup
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Ricrea con nuova strategia
        await createPeerConnection(nextStrategy);

        // Se siamo l'initiator, ricrea offer con ICE restart
        if (partnerId) {
            try {
                makingOffer = true;
                const offer = await peerConnection.createOffer({
                    iceRestart: true,
                    offerToReceiveAudio: true,
                    offerToReceiveVideo: true
                });

                await peerConnection.setLocalDescription(offer);
                socket.emit("offer", { offer, to: partnerId });

                console.log(`🚀 Nuovo offer inviato con strategia ${nextStrategy.toUpperCase()}`);
            } finally {
                makingOffer = false;
            }
        }

    } catch (error) {
        console.error("❌ Errore durante escalation:", error);

        // Se tutti i tentativi falliscono, cerca nuovo partner
        if (attemptNumber >= 4) {
            showNotification("Connessione impossibile. Cerco nuovo partner...", "error");
            cleanUpCall("Connessione fallita dopo tutti i tentativi");
            setTimeout(findMatch, 3000);
        }
    }
}

// Gestione fallimenti di connessione con retry automatico
let connectionRetryCount = 0;
const MAX_RETRY_ATTEMPTS = 3;

async function handleConnectionFailure() {
    console.log(`Tentativo di riconnessione ${connectionRetryCount + 1}/${MAX_RETRY_ATTEMPTS}`);

    if (connectionRetryCount < MAX_RETRY_ATTEMPTS) {
        connectionRetryCount++;
        showNotification(`Riconnessione in corso... (${connectionRetryCount}/${MAX_RETRY_ATTEMPTS})`, "info");

        // Cleanup connessione corrente
        if (peerConnection) {
            peerConnection.close();
            peerConnection = null;
        }

        // Attendi un po' prima di ritentare
        await new Promise(resolve => setTimeout(resolve, 2000));

        try {
            // Ricrea la connessione con configurazione più aggressiva per TURN
            await createPeerConnection();

            // Se siamo l'initiator, ricrea l'offer
            if (partnerId) {
                try {
                    makingOffer = true;
                    const offer = await peerConnection.createOffer({
                        iceRestart: true // Forza restart ICE
                    });
                    await peerConnection.setLocalDescription(offer);
                    socket.emit("offer", { offer, to: partnerId });
                } finally {
                    makingOffer = false;
                }
            }
        } catch (error) {
            console.error("Errore durante retry:", error);
            handleConnectionFailure(); // Riprova ricorsivamente
        }
    } else {
        console.log("Massimo numero di tentativi raggiunto");
        connectionRetryCount = 0;
        cleanUpCall("Impossibile stabilire la connessione. Cerco un nuovo partner...");
        setTimeout(findMatch, 3000);
    }
}

async function handleMatchFound(data) {
    console.log("🎯 Match trovato!", data);
    partnerId = data.partnerId;
    isInitiator = !!data.isInitiator;
    isPolite = !isInitiator; // the non-initiator acts as polite endpoint for glare handling
    connectionRetryCount = 0;
    connectionAttempts = 0;

    // 🧠 ANALISI INTELLIGENTE PRE-CONNESSIONE
    if (typeof window.MobileWebRTCConfig !== 'undefined') {
        const connInfo = window.MobileWebRTCConfig.getAdvancedConnectionInfo();
        console.log(`🔍 Analisi pre-connessione: ${connInfo.connectionQuality} (difficoltà: ${connInfo.difficultyScore}/100)`);

        if (connInfo.difficultyScore > 70) {
            showNotification("Connessione difficile rilevata. Ottimizzazione in corso...", "info");
        }
    }

    try {
        // 🚀 AVVIA CONNESSIONE CON SISTEMA INTELLIGENTE
        await createPeerConnection('adaptive');

        if (data.isInitiator) {
            // Configurazione offer ultra-ottimizzata
            const offerOptions = {
                offerToReceiveAudio: true,
                offerToReceiveVideo: true,
                iceRestart: false
            };

            try {
                makingOffer = true;
                const offer = await peerConnection.createOffer(offerOptions);
                await peerConnection.setLocalDescription(offer);

                console.log("🚀 Invio offer con sistema intelligente");
                socket.emit("offer", { offer, to: partnerId });
            } finally {
                makingOffer = false;
            }
        }
    } catch (err) {
        console.error("❌ Errore durante match found:", err);
        showNotification("Errore durante la connessione", "error");
        handleConnectionFailure();
    }
}

async function handleMatchProposal(data) {
    console.log('📨 Ricevuta match-proposal', data);
    try {
        const propId = data.proposalId;
        partnerId = data.partnerId;
        // Show UI state to indicate proposal received
        isSearching = false;
        updateCallControls();
        const remotePlaceholder = document.getElementById('remotePlaceholder');
        if (remotePlaceholder) {
            remotePlaceholder.style.display = 'block';
            remotePlaceholder.querySelector('h3').textContent = 'Proposta di match...';
            remotePlaceholder.querySelector('p').textContent = 'Coordinamento con il partner in corso...';
        }

        // Ensure local stream and peer connection are ready before confirming
        if (!cameraActive) {
            const ok = await startLocalStream();
            if (!ok) {
                showNotification('Impossibile attivare la videocamera, annullo match', 'error');
                return;
            }
        }

        if (!peerConnection) {
            await createPeerConnection('adaptive');
        }

        // Inform server we're ready for this proposal
        if (socket && socket.emit) {
            socket.emit('match-ready', { proposalId: propId });
            console.log('✅ Inviato match-ready per proposal', propId);
        }

        // Update UI
        if (remotePlaceholder) {
            remotePlaceholder.querySelector('h3').textContent = 'In attesa conferma...';
        }

    } catch (e) {
        console.error('Errore handleMatchProposal', e);
        showNotification('Errore interno durante proposta match', 'error');
    }
}

async function handleOffer(data) {
    console.log("📞 Ricevuto offer da:", data.from);
    metrics.offersReceived = (metrics.offersReceived || 0) + 1;

    try {
    partnerId = data.from;
    isInitiator = false;
    // polite is the inverse of initiator: non-initiator should be polite
    isPolite = !isInitiator;

        // Assicurati che la peer connection sia pronta
        if (!peerConnection) {
            console.log("Creazione PeerConnection per offer");
            await createPeerConnection();
        }

        // Polite glare handling
        const isStable = peerConnection.signalingState === 'stable';
        if (makingOffer || !isStable) {
            // Statistico glare
            metrics.glareCount = (metrics.glareCount || 0) + 1;
            if (!isPolite) {
                console.log('Impolite peer - ignorando offer per evitare glare');
                // Mark that we ignored the offer so subsequent answers are ignored
                ignoreOffer = true;
                metrics.ignoredOffers = (metrics.ignoredOffers || 0) + 1;
                return;
            } else {
                console.log('Polite peer - rollback locale prima di applicare remote offer');
                try {
                    await peerConnection.setLocalDescription({ type: 'rollback' });
                } catch (e) {
                    console.warn('Rollback non supportato o fallito', e);
                }
            }
        }

        // Timeout per operazioni WebRTC su mobile
        const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('Timeout WebRTC operation')), 15000);
        });

        const webrtcPromise = async () => {
            await peerConnection.setRemoteDescription(new RTCSessionDescription(data.offer));
            console.log("✅ Remote description impostata");
            // Clear ignoreOffer because we've successfully applied a remote offer
            ignoreOffer = false;
            // Process any ICE candidates that arrived early for this partner
            processQueuedIceCandidates(partnerId);

            // Configurazione answer ottimizzata
            const answerOptions = {
                offerToReceiveAudio: true,
                offerToReceiveVideo: true
            };

            const answer = await peerConnection.createAnswer(answerOptions);
            await peerConnection.setLocalDescription(answer);
            console.log("✅ Answer creato e impostato");

            socket.emit("answer", { answer, to: partnerId });
            console.log("✅ Answer inviato");
        };

        // Esegui con timeout
        await Promise.race([webrtcPromise(), timeoutPromise]);

    } catch (err) {
        console.error("❌ Errore gestione offer:", err);
        showNotification("Errore durante la connessione", "error");

        // Cleanup in caso di errore
        if (peerConnection) {
            peerConnection.close();
            peerConnection = null;
        }

        // Riprova o cerca nuovo partner
        handleConnectionFailure();
    }
}

async function handleAnswer(data) {
    console.log("📞 Ricevuto answer da:", data.from);

    try {
        if (!peerConnection) {
            console.error("❌ PeerConnection non disponibile per answer");
            return;
        }

        if (ignoreOffer) {
            console.log('Ignoro answer perché l\'offer precedente è stato ignorato');
            return;
        }

        if (peerConnection.signalingState === 'have-local-offer') {
            console.log("✅ Impostazione remote description con answer");
            await peerConnection.setRemoteDescription(new RTCSessionDescription(data.answer));
            console.log("✅ Answer processato correttamente");
            // Process queued ICE candidates after setting remote description
            processQueuedIceCandidates(data.from || partnerId);
        } else {
            console.warn(`⚠️ Stato signaling non corretto per answer: ${peerConnection.signalingState}`);
        }
    } catch (err) {
        console.error("❌ Errore gestione answer:", err);
        showNotification("Errore durante la connessione", "error");
        handleConnectionFailure();
    }
}

async function handleIceCandidate(data) {
    console.log("🧊 Ricevuto ICE candidate da:", data.from);

    try {
        if (!peerConnection) {
            // PeerConnection non ancora pronta: queue il candidate per il partner
            console.log("🧊 PeerConnection non pronta, mettendo in coda ICE candidate");
            queueIceCandidate(data.from || 'pending', data.candidate);
            return;
        }

        if (peerConnection.connectionState === 'closed') {
            console.log("⚠️ PeerConnection chiusa, ignorando ICE candidate");
            return;
        }

        if (data.candidate) {
            // If we don't have a remote description yet, queue this candidate
            const remoteDesc = peerConnection.remoteDescription;
            if (!remoteDesc || !remoteDesc.type) {
                console.log('🧊 Remote description non pronta, mettendo ICE candidate in coda');
                queueIceCandidate(data.from || partnerId || 'pending', data.candidate);
                return;
            }

            if (ignoreOffer) {
                console.log('🧊 Ignoro ICE candidate perché l\'offer precedente è stato ignorato');
                return;
            }

            const candidate = new RTCIceCandidate(data.candidate);
            console.log(`🧊 Aggiungendo ICE candidate: ${candidate.type} - ${candidate.protocol}`);

            await peerConnection.addIceCandidate(candidate);
            console.log("✅ ICE candidate aggiunto con successo");
        } else {
            console.log("🧊 ICE candidate finale (null) ricevuto");
        }
    } catch (err) {
        // Gli errori ICE sono comuni e spesso non critici
        console.warn("⚠️ Errore aggiunta ICE candidate (non critico):", err.message);

        // Non fare cleanup per errori ICE, potrebbero essere temporanei
        // Solo logga per debug
        if (err.name === 'OperationError') {
            console.log("ICE candidate probabilmente duplicato o non valido");
        }
    }
}

async function findMatch() {
    if (isSearching || isInCall) return;

    // Ensure Socket.IO client library is loaded
    if (typeof io === 'undefined') {
        try {
            // Prefer local vendor copy if present to avoid CDN dependency
            await loadExternalScript('/vendor/socket.io.min.js');
        } catch (errLocal) {
            try {
                await loadExternalScript('https://cdn.socket.io/4.7.2/socket.io.min.js');
            } catch (errCdn) {
                console.error('Errore caricamento Socket.IO client (local & CDN):', errLocal, errCdn);
                showNotification('Impossibile caricare il client di signaling.', 'error');
                return;
            }
        }
    }

    // Initialize socket if not already
    if (!socketInitialized) {
        try {
            await initializeSocket();
        } catch (err) {
            console.error('Errore inizializzazione socket:', err);
            showNotification('Impossibile connettersi al signaling server.', 'error');
            return;
        }
    }

    const startSearch = () => {
        isSearching = true;
        updateCallControls();
        document.getElementById("remotePlaceholder").querySelector("h3").textContent = "Ricerca in corso...";
        if (socket && socket.id) socket.emit("find-match", { userId: socket.id });
    };

    if (!cameraActive) {
        startLocalStream().then(success => {
            if (success) startSearch();
        });
    } else {
        startSearch();
    }
}

function cleanUpCall(message = "Chiamata terminata.") {
    console.log("🧹 Cleanup connessione:", message);

    // 🛑 FERMA SISTEMA ESCALATION
    if (escalationManager) {
        escalationManager.stop();
        escalationManager = null;
    }

    // 🔒 CHIUDI PEER CONNECTION
    if (peerConnection) {
        peerConnection.close();
        peerConnection = null;
    }

    // 🔄 RESET STATI
    isInCall = false;
    isSearching = false;
    explicitActive = false;
    explicitPending = false;
    // Clear queued ICE candidates for the current partner
    if (partnerId) iceCandidateQueue.delete(partnerId);
    partnerId = null;
    isInitiator = false;
    connectionRetryCount = 0;
    connectionAttempts = 0;

    // Reset offer/ignore state for safety
    makingOffer = false;
    ignoreOffer = false;

    // Stop stats monitor
    stopStatsMonitor();

    // 🎥 CLEANUP UI
    const remoteVideo = document.getElementById("remoteVideo");
    if (remoteVideo) {
        remoteVideo.srcObject = null;
        remoteVideo.style.display = "none";
    }

    const remotePlaceholder = document.getElementById("remotePlaceholder");
    if (remotePlaceholder) {
        remotePlaceholder.style.display = "block";
        const h3 = remotePlaceholder.querySelector("h3");
        const p = remotePlaceholder.querySelector("p");
        if (h3) h3.textContent = "In attesa...";
        if (p) p.textContent = message;
    }

    const chatMessages = document.getElementById("chatMessages");
    if (chatMessages) chatMessages.innerHTML = "";

    updateCallControls();

}
// --- Azioni globali per pulsanti Next/End ottimizzate ---
let actionLock = false;
function ensureSocketReady() {
    if (!socket || (socket && socket.disconnected)) {
        try { initializeSocket(); } catch (e) { console.warn('Socket init deferred', e); }
    }
}

function skipUser() {
    ensureSocketReady();
    if (!socket || !isInCall || actionLock) return;
    actionLock = true;
    // Teardown immediato per feedback UI
    cleanUpCall("Cerco un nuovo partner...");
    // Fallback: se entro 800ms non arriva un nuovo match, chiedi esplicitamente un find-match
    const fallback = setTimeout(() => { if (!isInCall && !isSearching) { try { findMatch(); } catch (e) {} } }, 450);
    // Ack dal server per next-partner (server risponde subito): cancella fallback
    try { socket.emit("next-partner", null, () => clearTimeout(fallback)); } catch (e) {}
    setTimeout(() => { actionLock = false; }, 180);
}

function endCall() {
    ensureSocketReady();
    if (actionLock) return;
    actionLock = true;
    if (isInCall) {
        cleanUpCall("Chiamata terminata.");
        try { socket && socket.emit("end-call", null, () => {}); } catch (e) {}
    }
    // Manteniamo la camera attiva per un re-ingaggio più rapido
    setTimeout(() => { actionLock = false; }, 180);
}

// Esponi globalmente per gli attributi onclick in dashboard.html
window.skipUser = skipUser;
window.endCall = endCall;
// Assicurati che findMatch sia globale
if (typeof window.findMatch === 'undefined') window.findMatch = findMatch;


// --- Modificato: rimuove query DOM eseguite all'import e usa variabili lazy ---
let dsaModal = null;
let submitDsaReportBtn = null;

function closeDsaReportModal() {
    closeModal("dsaReportModal");
    const rc = document.getElementById("report-category");
    const rd = document.getElementById("report-description");
    if (rc) rc.value = "";
    if (rd) rd.value = "";
}

function submitDsaReport() {
    const rc = document.getElementById("report-category");
    const rd = document.getElementById("report-description");
    if (!rc || !rd) return;

    const reportData = {
        category: rc.value,
        description: rd.value,
        userId: generateUserId(),
        timestamp: new Date().toISOString()
    };

    // Disabilita il pulsante di invio per prevenire invii multipli
    if (submitDsaReportBtn) {
        submitDsaReportBtn.disabled = true;
    }

    // Invia il report al server
    fetch('/api/dsa/report', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(reportData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification("Segnalazione inviata con successo", "success");
            closeDsaReportModal();
        } else {
            throw new Error(data.error || "Errore sconosciuto");
        }
    })
    .catch(error => {
        console.error("Errore invio segnalazione:", error);
        showNotification("Errore durante l'invio della segnalazione", "error");
    })
    .finally(() => {
        // Riabilita il pulsante di invio
        if (submitDsaReportBtn) {
            submitDsaReportBtn.disabled = false;
        }
    });
}

document.addEventListener("DOMContentLoaded", () => {
    console.log("🚀 VIDEOCHAT ELITE - SISTEMA ULTRA-INTELLIGENTE ATTIVATO");

    // 🍎 MOSTRA PULSANTE iOS SE NECESSARIO
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
    if (isIOS) {
        const iosBtn = document.getElementById("iosBtn");
        if (iosBtn) {
            iosBtn.style.display = "inline-block";
            console.log("🍎 Pulsante iOS attivato");
        }
    }

    // 🔬 AVVIA DIAGNOSTICA
    startAdvancedDiagnostics();

    // Socket will be initialized lazily when user starts a match to reduce initial load
    updateCallControls();

    const chatSendBtn = document.getElementById("chatSendBtn");
    if (chatSendBtn) chatSendBtn.addEventListener("click", sendChatMessage);

    const chatInputEl = document.getElementById("chatInput");
    if (chatInputEl) {
        chatInputEl.addEventListener("keypress", e => {
            if (e.key === "Enter" && !e.shiftKey) {
                e.preventDefault();
                sendChatMessage();
            }
        });
    }

function addChatMessage(sender, message, who = 'self') {
    const box = document.getElementById('chatMessages');
    if (!box) return;
    const row = document.createElement('div');
    row.className = `chat-row ${who}`;
    row.innerHTML = `<strong>${sender}:</strong> ${escapeHtml(message)}`;
    box.appendChild(row);
    box.scrollTop = box.scrollHeight;
}
function sendChatMessage() {
    const input = document.getElementById('chatInput');
    if (!input || !input.value.trim() || !socket || !isInCall) return;
    const msg = input.value.trim();
    try {
        socket.emit('chat-message', { to: partnerId, message: msg });
        addChatMessage('You', msg, 'self');
        input.value = '';
    } catch(e) { console.warn('chat send failed', e); }
}
function escapeHtml(s) {
    return s.replace(/[&<>"]+/g, c => ({'&':'&amp;','<':'&lt;','>':'&gt;','"':'&quot;'}[c]));
}

    // --- Inizializza la modal DSA e i listener solo se l'elemento esiste ---
    dsaModal = document.getElementById("dsaReportModal");
    submitDsaReportBtn = document.getElementById("submitDsaReportBtn");

    if (dsaModal) {
        const closeBtn = dsaModal.querySelector(".report-close-btn");
        const cancelBtn = dsaModal.querySelector(".report-btn-cancel");
        const overlay = dsaModal.querySelector(".report-modal-overlay");
        if (closeBtn) closeBtn.addEventListener("click", closeDsaReportModal);
        if (cancelBtn) cancelBtn.addEventListener("click", closeDsaReportModal);
        if (overlay) overlay.addEventListener("click", closeDsaReportModal);
    }

    if (submitDsaReportBtn) {
        submitDsaReportBtn.addEventListener("click", submitDsaReport);
    }

    // 🎯 LOG SISTEMA PRONTO
    console.log("✅ Sistema di connessione intelligente pronto!");
});

window.addEventListener("beforeunload", () => {
    if (socket) socket.disconnect();
    // Ferma i media solo quando si lascia la pagina per davvero
    if (localStream) localStream.getTracks().forEach(t => t.stop());
});