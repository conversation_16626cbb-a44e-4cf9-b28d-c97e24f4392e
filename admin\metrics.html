<!doctype html>
<html>
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <title>Admin Metrics - VideoChat Elite</title>
  <style>
    body { font-family: system-u<PERSON>,Segoe UI,Roboto,Arial; padding: 20px; background:#fafafa; color:#111 }
    .card { background: #fff; padding:16px; border-radius:8px; box-shadow: 0 2px 8px rgba(0,0,0,0.06); margin-bottom:12px }
    .grid { display:flex; gap:12px; flex-wrap:wrap }
    .metric { flex:1 1 200px }
    pre { background:#0f172a; color:#d1fae5; padding:12px; border-radius:6px; max-height:240px; overflow:auto }
    button { padding:8px 12px; border-radius:6px; border:1px solid #ddd; background:#fff }
  </style>
</head>
<body>
  <h1>Admin Metrics</h1>
  <div class="card grid">
    <div class="metric card">Offers Received: <strong id="offers">0</strong></div>
    <div class="metric card">Glare Count: <strong id="glare">0</strong></div>
    <div class="metric card">Ignored Offers: <strong id="ignored">0</strong></div>
    <div class="metric card">ICE Queued: <strong id="queued">0</strong></div>
  </div>

  <div class="card">
    <h3>Recent Samples</h3>
    <pre id="samples">loading...</pre>
  </div>

  <div class="card">
    <button id="refresh">Refresh Now</button>
  </div>

  <script>
    async function fetchMetrics() {
      try {
        const res = await fetch('/api/metrics');
        const j = await res.json();
        if (!j.success) return;
        const m = j.metrics;
        document.getElementById('offers').textContent = m.offersReceived || 0;
        document.getElementById('glare').textContent = m.glareCount || 0;
        document.getElementById('ignored').textContent = m.ignoredOffers || 0;
        document.getElementById('queued').textContent = m.iceCandidatesQueued || 0;
        const samples = (m.samples || []).slice(-20).map(s => JSON.stringify(s)).join('\n\n');
        document.getElementById('samples').textContent = samples || 'no samples';
      } catch (e) {
        document.getElementById('samples').textContent = 'fetch error: ' + e.message;
      }
    }

    document.getElementById('refresh').addEventListener('click', fetchMetrics);
    fetchMetrics();

    // Live updates via socket.io
    (function() {
      function attachSocket() {
        try {
          const socket = io('/');
          socket.on('connect', () => console.log('admin connected'));
          socket.on('metrics-updated', d => {
            document.getElementById('offers').textContent = Number(document.getElementById('offers').textContent) + (d.metrics.offersReceived || 0);
            document.getElementById('glare').textContent = Number(document.getElementById('glare').textContent) + (d.metrics.glareCount || 0);
            document.getElementById('ignored').textContent = Number(document.getElementById('ignored').textContent) + (d.metrics.ignoredOffers || 0);
            document.getElementById('queued').textContent = Number(document.getElementById('queued').textContent) + (d.metrics.iceCandidatesQueued || 0);
            const prev = document.getElementById('samples').textContent;
            document.getElementById('samples').textContent = (JSON.stringify(d) + '\n\n' + prev).slice(0, 4000);
          });
        } catch (e) {
          console.warn('socket attach failed', e);
        }
      }

      const s = document.createElement('script');
      s.src = '/vendor/socket.io.min.js';
      s.onload = attachSocket;
      s.onerror = () => {
        console.warn('socket.io vendor missing, falling back to CDN');
        const c = document.createElement('script');
        c.src = 'https://cdn.socket.io/4.7.2/socket.io.min.js';
        c.onload = attachSocket;
        c.onerror = () => console.warn('socket.io CDN load failed');
        document.head.appendChild(c);
      };
      document.head.appendChild(s);
    })();
  </script>
</body>
</html>
