/**
 * 🚀 SISTEMA CACHE BUSTING AUTOMATICO ULTRA-INTELLIGENTE
 * Forza il refresh della cache automaticamente dal server
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

class CacheBusterManager {
    constructor() {
        this.version = Date.now(); // Timestamp come versione
        this.fileHashes = new Map();
        this.watchedFiles = [
            'dashboard.js',
            'mobile_webrtc_config.js',
            'dashboard.html',
            'index.html',
            'mobile_patch.js',
            'webrtc_fixes.js'
        ];
        
        console.log('🚀 Cache Buster Manager inizializzato');
        this.generateFileHashes();
    }
    
    // 🔍 Genera hash per ogni file per rilevare modifiche
    generateFileHashes() {
        this.watchedFiles.forEach(filename => {
            try {
                const filePath = path.join(__dirname, filename);
                if (fs.existsSync(filePath)) {
                    const content = fs.readFileSync(filePath, 'utf8');
                    const hash = crypto.createHash('md5').update(content).digest('hex').substring(0, 8);
                    this.fileHashes.set(filename, hash);
                    console.log(`📁 ${filename}: ${hash}`);
                }
            } catch (error) {
                console.warn(`⚠️ Impossibile leggere ${filename}:`, error.message);
            }
        });
    }
    
    // 🔄 Aggiorna versione (chiamata quando si rilevano modifiche)
    updateVersion() {
        this.version = Date.now();
        this.generateFileHashes();
        console.log(`🔄 Cache version aggiornata: ${this.version}`);
        return this.version;
    }
    
    // 📝 Ottieni URL con cache busting
    getCacheBustedUrl(filename) {
        const hash = this.fileHashes.get(filename) || 'unknown';
        return `${filename}?v=${this.version}&h=${hash}`;
    }
    
    // 🎯 Genera HTML con cache busting automatico ULTRA-AGGRESSIVO
    injectCacheBusting(htmlContent) {
        let modifiedHtml = htmlContent;

        // 🚀 Sostituisci TUTTI i riferimenti ai file JS con versioni cache-busted
        this.watchedFiles.forEach(filename => {
            if (filename.endsWith('.js')) {
                // Pattern multipli per catturare tutti i riferimenti
                const patterns = [
                    `src="${filename}"`,
                    `src='${filename}'`,
                    `src="${filename}?`,
                    `src='${filename}?`
                ];

                patterns.forEach(pattern => {
                    const cachedRef = `src="${this.getCacheBustedUrl(filename)}"`;
                    modifiedHtml = modifiedHtml.replace(new RegExp(pattern.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), cachedRef);
                });

                console.log(`🔧 ${filename} → ${this.getCacheBustedUrl(filename)}`);
            }
        });

        // 🛡️ Meta tag ULTRA-AGGRESSIVI per forzare no-cache
        const noCacheMeta = `
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate, max-age=0, s-maxage=0">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <meta name="cache-version" content="${this.version}">
    <meta name="cache-timestamp" content="${Date.now()}">
    <meta name="last-modified" content="${new Date().toISOString()}">`;

        // Rimuovi eventuali meta tag esistenti per evitare duplicati
        modifiedHtml = modifiedHtml.replace(/<meta[^>]*cache[^>]*>/gi, '');
        modifiedHtml = modifiedHtml.replace('</head>', `${noCacheMeta}\n</head>`);

        return modifiedHtml;
    }
    
    // 🔍 Controlla se i file sono stati modificati
    checkForUpdates() {
        let hasChanges = false;
        
        this.watchedFiles.forEach(filename => {
            try {
                const filePath = path.join(__dirname, filename);
                if (fs.existsSync(filePath)) {
                    const content = fs.readFileSync(filePath, 'utf8');
                    const newHash = crypto.createHash('md5').update(content).digest('hex').substring(0, 8);
                    const oldHash = this.fileHashes.get(filename);
                    
                    if (newHash !== oldHash) {
                        console.log(`🔄 File modificato: ${filename} (${oldHash} → ${newHash})`);
                        hasChanges = true;
                    }
                }
            } catch (error) {
                console.warn(`⚠️ Errore controllo ${filename}:`, error.message);
            }
        });
        
        if (hasChanges) {
            this.updateVersion();
        }
        
        return hasChanges;
    }
    
    // 🎯 Middleware Express per cache busting automatico
    getExpressMiddleware() {
        return (req, res, next) => {
            // Controlla aggiornamenti ogni richiesta (in sviluppo)
            if (process.env.NODE_ENV !== 'production') {
                this.checkForUpdates();
            }

            // 🎯 GESTIONE ROUTING CORRETTA
            let targetFile = null;
            let isHtmlRequest = false;

            if (req.url === '/' || req.url.startsWith('/?')) {
                // Root va all'index
                targetFile = 'index.html';
                isHtmlRequest = true;
                console.log('📄 Richiesta ROOT → Servendo INDEX.HTML');
            } else if (req.url === '/dashboard.html' ||
                      req.url === '/dashboard' ||
                      req.url.startsWith('/dashboard.html?')) {
                // Dashboard esplicita
                targetFile = 'dashboard.html';
                isHtmlRequest = true;
                console.log('📄 Richiesta DASHBOARD → Servendo DASHBOARD.HTML');
            }

            if (isHtmlRequest && targetFile) {
                try {
                    const htmlPath = path.join(__dirname, targetFile);

                    // Verifica che il file esista
                    if (!fs.existsSync(htmlPath)) {
                        console.log(`⚠️ File ${targetFile} non trovato, fallback a dashboard.html`);
                        targetFile = 'dashboard.html';
                    }

                    let htmlContent = fs.readFileSync(path.join(__dirname, targetFile), 'utf8');

                    // Applica cache busting
                    htmlContent = this.injectCacheBusting(htmlContent);

                    // Imposta header no-cache ULTRA-AGGRESSIVI
                    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate, max-age=0, s-maxage=0');
                    res.setHeader('Pragma', 'no-cache');
                    res.setHeader('Expires', '0');
                    res.setHeader('Last-Modified', new Date().toUTCString());
                    res.setHeader('ETag', `"${this.version}"`);
                    res.setHeader('Content-Type', 'text/html; charset=utf-8');

                    console.log(`📄 Servendo dashboard.html con cache version: ${this.version} per ${req.url}`);
                    return res.send(htmlContent);

                } catch (error) {
                    console.error('❌ Errore cache busting:', error);
                    return next(); // Fallback al comportamento normale
                }
            }

            // 🚀 Per file JS/CSS, aggiungi header no-cache ULTRA-AGGRESSIVI
            if (req.url.endsWith('.js') || req.url.endsWith('.css')) {
                res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate, max-age=0, s-maxage=0');
                res.setHeader('Pragma', 'no-cache');
                res.setHeader('Expires', '0');
                res.setHeader('Last-Modified', new Date().toUTCString());

                console.log(`📁 Servendo ${req.url} con no-cache headers`);
            }

            next();
        };
    }
    
    // 🔄 Forza refresh cache (chiamabile manualmente)
    forceRefresh() {
        console.log('🔄 FORZANDO REFRESH CACHE...');
        this.updateVersion();
        return {
            success: true,
            version: this.version,
            message: 'Cache forzatamente aggiornata'
        };
    }
    
    // 📊 Ottieni stato cache
    getStatus() {
        return {
            version: this.version,
            filesWatched: this.watchedFiles.length,
            fileHashes: Object.fromEntries(this.fileHashes),
            lastUpdate: new Date(this.version).toISOString()
        };
    }
}

// Esporta singleton
const cacheBuster = new CacheBusterManager();

module.exports = {
    CacheBusterManager,
    cacheBuster
};
