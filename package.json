{"dependencies": {"@socket.io/redis-adapter": "^8.3.0", "compression": "^1.8.1", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "mongoose": "^8.16.3", "puppeteer": "^24.15.0", "redis": "^5.7.0", "sanitize-html": "^2.17.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "sqlite3": "^5.1.7", "uuid": "^11.1.0"}, "devDependencies": {"esbuild": "^0.19.0"}, "scripts": {"start": "node server.js", "dev": "nodemon server.js", "build:client": "esbuild dashboard.js --bundle --minify --sourcemap --outfile=dist/dashboard.bundle.js", "vendorize": "node ./scripts/vendorize.js"}}